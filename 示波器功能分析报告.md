# 示波器功能分析报告

## 1. 概述

本文档详细分析了 `ServoStudio` 软件中示波器功能的实现。示波器是该软件的核心功能之一，用于实时监控和分析伺服驱动器的各项关键参数，如位置、速度、转矩等。它不仅提供数据可视化，还集成了函数发生器、三环调试、运动调试和参数调优等高级功能，是进行伺服系统调试和优化的关键工具。

## 2. 核心组件与文件结构

示波器功能的实现主要涉及以下几个核心文件，它们遵循MVVM设计模式：

-   **View (视图):** `ServoStudio/Views/OscilloscopeView.xaml`
    -   负责定义用户界面（UI），包括图表、按钮、下拉框、文本框等所有可见元素。
    -   使用 `DevExpress` 控件库构建界面，特别是图表部分使用了 `DynamicDataDisplay` (D3) 库。
    -   通过数据绑定（Binding）与 `OscilloscopeViewModel` 进行交互。

-   **ViewModel (视图模型):** `ServoStudio/ViewModels/OscilloscopeViewModel.cs`
    -   示波器功能的核心逻辑层，负责处理所有UI交互、业务逻辑和状态管理。
    -   定义了大量的可观察属性（Observable Properties）来与视图进行数据绑定，例如采样通道、采样周期、触发器设置等。
    -   包含了处理用户操作（如开始/停止采集、导入/导出波形）的命令（Commands）。
    -   管理函数发生器、三环调试、运动调试等子功能的UI逻辑和参数。

-   **Model (模型):** `ServoStudio/Models/OscilloscopeModel.cs`
    -   定义了示波器功能所需的数据结构和实体类。
    -   `AcquisitionInfoSet`: 存储当前采集任务的静态信息，如采样通道、采样点数、是否连续采集等。
    -   `AcquisitionData`: 静态类，用于存储从硬件上传的原始波形数据。
    -   `OfflineOscilloscope`: 用于处理波形数据的导入和导出，定义了离线波形文件的数据结构。
    -   `OscilloscopeParameterSet`: 封装了单次采集的所有参数和数据，用于保存和载入。

-   **Communication (通信):** `ServoStudio/ViewModels/CommunicationSetViewModel.cs`
    -   负责与硬件设备进行串行通信。
    -   `SerialPort_DataTransmiting_For_ParameterAcquisition`: 封装并发送数据采集指令（功能码 `0x01`）。
    -   `AnalyseReceivingMessage`: 解析从硬件返回的数据。当识别到数据采集相关的响应时，它会处理数据、更新任务状态，并触发相应的回调或事件，将数据传递给 `OscilloscopeViewModel` 进行处理和显示。

## 3. 数据采集流程分析

示波器的数据采集是一个典型的“请求-执行-上传-处理”的异步流程，由用户在UI上的操作触发，通过 `CommunicationSetViewModel` 与硬件交互，最终将数据显示在 `OscilloscopeView` 上。

### 3.1. 采集启动 (`ParameterAcquisitionStart` 方法)

1.  **前置检查:**
    *   检查串口是否连接。
    *   检查是否已存在其他采集任务 (`AcquisitionInfoSet.IsExistTask`)。
    *   检查上一次的波形是否已绘制完成 (`AcquisitionInfoSet.IsDrawingCompleted`)，防止数据覆盖。

2.  **参数打包:**
    *   调用 `RefreshAcquisitionList` 方法，根据UI上选择的通道，更新 `AcquisitionInfoSet` 中的通道列表、单位列表和换算系数列表。
    *   调用 `GetTransmittingContent` 方法，将UI上的采样设置（采样周期、时长、触发模式、触发电平等）打包成一个符合通信协议的16进制字符串。
        *   **采样总长 (sSampingLength):** 根据采样时长和采样周期计算得出。
        *   **触发电平 (iTriggerLevel):** 会根据所选通道的单位进行换算，确保发送给硬件的是标准单位下的数值。

3.  **任务下发:**
    *   调用 `CommunicationSetViewModel.SerialPort_DataTransmiting_For_ParameterAcquisition` 方法。
    *   该方法会将打包好的报文和任务信息（任务名: `AssigningAcquisition`，功能码: `0x01`）添加到一个全局的串口任务队列 `SerialPortTask.TaskManagement` 中。
    *   后台的发送线程 (`PthreadStatement.SerialPortTransmiting`) 会轮询此队列，取出任务并发送给硬件。

4.  **状态更新:**
    *   禁用UI上的部分按钮（如导入、导出、清除等），防止在采集中进行冲突操作。
    *   调用 `RecordLastSamplingParameter` 方法，将本次的采样设置保存到 `OfflineOscilloscope.Last` 对象中，以便后续可以“载入上一次波形”。

### 3.2. 硬件数据上传与处理

1.  **硬件执行与响应:** 硬件接收到采集指令后，开始按照设定的参数进行数据采集。采集完成后，硬件准备好数据等待上位机读取。

2.  **状态查询与数据请求 (轮询机制):**
    *   `OscilloscopeViewModel` 启动采集后，`CommunicationSetViewModel` 会周期性地发送“查询采集状态”的指令 (`AcquisitionExecutedCode.ASK_ACQUISITION`)。
    *   硬件响应此查询，返回当前状态（如：正在采集、采集完毕、无任务等）。

3.  **数据上传:**
    *   当 `CommunicationSetViewModel` 收到“采集完毕”的状态后，会启动数据上传流程。
    *   它会发送“上传数据”的指令 (`AcquisitionExecutedCode.UPLOAD_ACQUISITION`)，并带上请求的数据包号。
    *   硬件根据包号返回对应的数据帧。

4.  **数据解析与存储 (`AnalyseReceivingMessage` 方法):**
    *   `CommunicationSetViewModel` 的 `SerialPortInfo_DataReceived` 事件处理器接收到数据。
    *   在 `AnalyseReceivingMessage` 方法中，根据功能码 `0x01` 和执行码 `0x03` (UPLOAD_ACQUISITION) 判断为数据上传响应。
    *   调用 `HexHelper.ReceivingMessage_ForUploading` 方法解析数据帧，将原始数据点存入 `AcquisitionInfoSet.lstReceiving` 列表中的对应通道。
    *   如果数据未上传完毕，会继续发送下一帧的数据上传请求，直到所有数据上传完成。

### 3.3. 波形显示

1.  **上传完成触发显示:** 当所有数据帧都成功接收后 (`ReceivingMessage_ForUploading` 返回 `100`)，表示数据上传完成。

2.  **数据显示调用:**
    *   **非连续采样:** 直接调用 `OscilloscopeView.DisplayOscilloscope()` 方法。
    *   **连续采样 (静态):** 调用 `DisplayOscilloscope()`，然后立即再次调用 `ParameterAcquisitionStart()` 发起下一次采集。
    *   **连续采样 (动态):** 调用 `OscilloscopeView.DisplayOscilloscopeLoop()`，并启动一个专门的UI线程 (`PthreadStatement.MicrosecondsOscilloscopeDrawing`) 来处理动态数据的渲染，同时在通信层调用 `LoopParameterAcquisitionStart()` 发起下一次采集。

3.  **`OscilloscopeView.xaml.cs` 中的绘制逻辑:**
    *   `DisplayOscilloscope()` 方法是最终的绘制入口。
    *   它会从 `AcquisitionData` 中获取所有通道的数据。
    *   对每个通道的数据进行处理：
        *   应用倍乘系数 (`OscilloscopeProperty.Doubling`)。
        *   根据单位和换算系数 (`AcquisitionInfoSet.lstUnit`, `AcquisitionInfoSet.lstExchangeValue`) 将原始数据转换为用户选择的物理单位值。
    *   将处理后的数据点 (`Point` 集合) 绑定到 `DynamicDataDisplay` (D3) 图表控件的 `DataSource`，从而在UI上绘制出波形。

## 4. 核心功能实现细节

### 4.1. 触发机制

-   **UI 设置:** 用户在 `OscilloscopeView` 上选择触发模式（上升沿、下降沿等）、触发通道和触发电平。
-   **参数打包:** 在 `GetTransmittingContent` 方法中，这些设置被转换为协议规定的字节码。
-   **硬件执行:** 触发逻辑完全由硬件实现。上位机只负责发送配置，不参与触发判断。硬件在接收到采集指令后，会等待满足触发条件的信号出现，然后才开始（或根据预触发设置）记录数据。

### 4.2. 预设配置

-   **数据存储:** 提供了6个预设配置槽位，所有配置信息（采样通道、周期、触发设置等）都通过 `XmlHelper` 类保存在一个XML文件中 (`Config/OscilloscopePresetConfigs.xml`)。
-   **加载与保存:**
    *   `OscilloscopeLoaded` 方法加载XML文件中的所有预设配置。
    *   当用户选择一个预设配置时，`OnSelectedOscilloscopePresetChanged` 方法会从加载的配置列表中读取对应的数据并更新UI。
    *   用户点击“保存”按钮时，`OscilloscopeConfigExport` 方法会将当前UI上的所有设置封装成 `OscilloscopePresetModel` 对象，并调用 `XmlHelper.EditOscilloscopePresetConfigs` 更新XML文件。

### 4.3. 函数发生器与三环/运动调试

-   示波器界面通过一个 `TabControl` 集成了这些高级调试功能。
-   **逻辑分离:** 每个功能（如函数发生器、三环调试）都有独立的参数设置区域和启动/停止按钮。
-   **参数读写:**
    *   当切换到相应功能的Tab页时，会触发 `OnSelectedTabIndexChanged` 事件，调用 `ReadAdjustmentParameter` 方法从硬件读取当前参数值并更新UI。
    *   用户修改参数后点击“下载参数”或“启动运动”等按钮，会调用相应的方法（如 `WriteLoopParameter`, `StartAction`）。
    *   这些方法会收集UI上的参数，打包成写指令（功能码 `0x03`），通过 `CommunicationSetViewModel` 发送给硬件。
-   **与示波器联动:** 这些调试功能启动后，用户可以同时使用示波器来观察参数变化对系统行为的影响，这是其核心价值所在。例如，在调整PID参数后，可以立即在示波器上观察速度或位置曲线的变化。

## 5. 总结

`ServoStudio` 的示波器功能是一个设计精良、功能强大的模块。它通过清晰的MVVM分层，将UI、业务逻辑和数据通信有效解耦。其核心数据流依赖于一个稳健的、基于任务队列的异步通信机制，确保了在进行高频率数据采集时UI的响应性。此外，它集成的多种高级调试工具与示波器本身无缝结合，为用户提供了一个一体化的伺服系统分析与优化平台。