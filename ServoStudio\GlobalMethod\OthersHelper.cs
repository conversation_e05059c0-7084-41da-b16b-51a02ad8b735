﻿using ServoStudio.GlobalConstant;
using ServoStudio.Models;
using ServoStudio.ViewModels;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Data;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;

namespace ServoStudio.GlobalMethod
{
    public static class OthersHelper
    {
        //*************************************************************************
        //函数名称：RetrieveDataTable
        //函数功能：检索 DataTable 
        //
        //输入参数：DataTable dt             检索前的数据集
        //                 string strCondition      检索条件
        //                 ref DataTable Out_dt     检索后的数据集
        //
        //输出参数：1:OK
        //         0:No_Effect
        //        -1:Error
        //        
        //编码作者：Ryan
        //更新时间：2019.10.28
        //*************************************************************************
        public static int RetrieveDataTable(DataTable In_dt, string strCondition, ref DataTable Out_dt)
        {
            try
            {
                if (In_dt == null)
                {
                    return RET.NO_EFFECT;
                }
                else if (In_dt.Rows.Count < 1)
                {
                    return RET.NO_EFFECT;
                }

                //string[] a = In_dt.AsEnumerable().Select(t => t.Field<string>("Classification")).Distinct().ToArray();

                DataRow[] Rows = In_dt.Select(strCondition);

                Out_dt = In_dt.Clone();
                foreach (DataRow DR in Rows)
                {
                    Out_dt.ImportRow(DR);
                }

                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OTHERSHELPER_RETRIEVE_DATATABLE, "OthersHelper.RetrieveDataTable", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：RetrieveDataTable
        //函数功能：检索 DataTable 
        //
        //输入参数：DataTable dt             检索前的数据集
        //                 string strCondition      检索条件
        //                 ref DataTable Out_dt     检索后的数据集
        //
        //输出参数：1:OK
        //         0:No_Effect
        //        -1:Error
        //        
        //编码作者：Lilbert
        //更新时间：2023.05.17
        //*************************************************************************
        public static int RetrieveDataTable_For_Compare(DataTable In_dt, string strCondition, ref DataTable Out_dt)
        {
            try
            {
                if (In_dt == null)
                {
                    return RET.NO_EFFECT;
                }
                else if (In_dt.Rows.Count < 1)
                {
                    return RET.NO_EFFECT;
                }

                //DataRow[] Rows = In_dt.Select(strCondition);
                DataRow[] Rows = In_dt.Select();

                Out_dt = In_dt.Clone();
                foreach (DataRow DR in Rows)
                {
                    Out_dt.ImportRow(DR);
                }

                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OTHERSHELPER_RETRIEVE_DATATABLE, "OthersHelper.RetrieveDataTable", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：AdjustTimeFormat
        //函数功能：调整时间模式
        //
        //输入参数：string In_strTime    时间
        //
        //输出参数：
        //        
        //编码作者：Ryan
        //更新时间：2019.10.28
        //*************************************************************************
        public static string AdjustTimeFormat(string In_strTime)
        {
            int i = In_strTime.IndexOf(" ");

            if (i >= 5)
            {
                return DateTime.Now.Year.ToString() + "/" + In_strTime.Remove(i - 5);
            }
            else
            {
                return In_strTime;
            }
        }

        //*************************************************************************
        //函数名称：RetrieveDataTable
        //函数功能：软件错误日志检索
        //
        //输入参数：string In_strBeginningDate   起始时间
        //                string In_strEndingDate      截止时间
        //                ref ObservableCollection<SoftwareErrorSet> Out_obsErrorLog   集合
        //         
        //输出参数：-1：WRONG
        //                  1: OK
        //                  0: No_Effect
        //        
        //编码作者：Ryan
        //更新时间：2020.10.19
        //*************************************************************************
        public static int RetrieveErrorLogByDatetime(string In_strBeginningDate, string In_strEndingDate, ref ObservableCollection<SoftwareErrorSet> Out_obsErrorLog)
        {
            int iRet = -1;
            DataTable dt = new DataTable();
            Out_obsErrorLog = new ObservableCollection<SoftwareErrorSet>();

            try
            {
                if (string.IsNullOrEmpty(In_strBeginningDate) || string.IsNullOrEmpty(In_strEndingDate))
                {
                    return RET.NO_EFFECT;
                }

                DateTime dtBeginning = Convert.ToDateTime(OthersHelper.AdjustTimeFormat(In_strBeginningDate) + " 00:00:00");
                DateTime dtEnding = Convert.ToDateTime(OthersHelper.AdjustTimeFormat(In_strEndingDate) + " 23:59:59");

                IEnumerable<DataRow> Rows = from d in GlobalErrorSet.dtSoftware.AsEnumerable()
                                            where Convert.ToDateTime(d.Field<string>("DateTime")).CompareTo(dtBeginning) > 0 && Convert.ToDateTime(d.Field<string>("DateTime")).CompareTo(dtEnding) < 0
                                            select d;

                dt = GlobalErrorSet.dtSoftware.Clone();
                foreach (DataRow dr in Rows)
                {
                    dt.ImportRow(dr);
                }

                iRet = ConvertHelper.DataTableToObservableCollection(dt, ref Out_obsErrorLog);
            }
            catch (System.Exception ex)
            {
                iRet = RET.ERROR;
                SoftwareErrorHelper.CatchDispose(ERROR.OTHERSHELPER_RETRIEVE_ERRORLOG_BY_DATETIME, "RetrieveErrorLogByDatetime", ex);
            }

            return iRet;
        }

        //*************************************************************************
        //函数名称：GetCellValueFromDataTable
        //函数功能：获取DataTable的Cell值
        //
        //输入参数：string In_strConditionName   检索的列名
        //         string In_strConidtionValue  列名的值
        //         string In_strColumnName      检索出的Row的列名
        //         DataTable In_dt              数据源
        //
        //输出参数：Cell的值
        //        
        //编码作者：Ryan
        //更新时间：2019.11.12
        //*************************************************************************
        public static string GetCellValueFromDataTable(DataTable In_dt, string In_strConditionName, string In_strConditionValue, string In_strColumnName)
        {
            DataRow[] Rows = null;
            In_strColumnName = ("\"" + In_strColumnName + "\"").Replace("\"", "");
            string strCondition = ("\"" + In_strConditionName + "='" + In_strConditionValue + "'\"").Replace("\"", "");

            try
            {
                if (In_dt == null)
                {
                    return "0";
                }

                if (In_dt.Rows.Count == 0)
                {
                    return "0";
                }

                Rows = In_dt.Select(strCondition);
                if (Rows.Length > 0)
                {
                    int iColumn = Rows[0].Table.Columns.IndexOf(In_strColumnName);
                    string strTemp = Convert.ToString(Rows[0][iColumn]);

                    if (string.IsNullOrEmpty(strTemp))
                    {
                        return "0";
                    }
                    else
                    {
                        return strTemp;
                    }
                }
                else
                {
                    return "0";
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OTHERSHELPER_GET_CELL_VALUE_FROM_DATATABLE, "GetCellValueFromDataTable", ex);
                return null;
            }
        }

        //*************************************************************************
        //函数名称：DataTableCopy
        //函数功能：DataTable复制
        //
        //输入参数：DataTable dt_Source          复制原DataTable
        //         ref DataTable dt_Target      复制目标DataTable
        //
        //输出参数：0：NO_EFFECT
        //         1: OK
        //        -1: NG
        //        
        //编码作者：Ryan
        //更新时间：2019.11.15
        //*************************************************************************
        public static int DataTableCopy(DataTable dt_Source, ref DataTable dt_Target)
        {
            dt_Target = new DataTable();

            try
            {
                if (dt_Source == null)
                {
                    return RET.NO_EFFECT;
                }
                else if (dt_Source.Rows.Count == 0)
                {
                    return RET.NO_EFFECT;
                }

                dt_Target = dt_Source.Clone();

                for (int i = 0; i < dt_Source.Rows.Count; i++)
                {
                    dt_Target.ImportRow(dt_Source.Rows[i]);
                }

                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OTHERSHELPER_DATATABLE_COPY, "DataTableCopy", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：Excel_For_Compare
        //函数功能：获取两个Excel的差异数据到Out_clsDataDiffTable
        //
        //输入参数：DataTable dt_DataImportTable           导入参数表  
        //          DataTable dt_DataBaseTable             导出参数表
        //          ref DataTable Out_clsDataImportTable   两表相同数据的数据表
        //          ref DataTable Out_clsDataImportTable   两表不相同数据的数据表
        //
        //输出参数：1:OK
        //         0:NO_EFFECT
        //        -1:Error
        //        
        //编码作者：Lilbert
        //更新时间：2023.05.17  
        //*************************************************************************
        public static int Excel_For_Compare(DataTable dt_DataImportTable, DataTable dt_DataBaseTable, ref DataTable Out_clsDataImportTable, ref DataTable Out_clsDataDiffTable)
        {
            bool bEmpty = true;//判断DataTable是否某行全部为空 若是：跳出
            bool isDiff = false;
            bool bDiff = false;
            int columnCount = 0;//列数
            int rowMinCount = 0;//最小行数
            int rowMaxCount = 0;//最大行数
            string strCondition = null;

            string[] arrImportClassification = null;
            DataRow[] arrImportDataRows = null;

            string[] arrBaseClassification = null;
            DataRow[] arrBaseDataRows = null;

            DataRow[] arrMaxDataRows = null;

            DataTable dt_OutImport = new DataTable();//对比后相同参数表
            DataTable dt_OutDiff = new DataTable();//对比后不相同参数表
            DataTable dt_OutTempDiff = new DataTable();

            dt_OutImport.Columns.Add(new DataColumn("Section", typeof(string)));
            dt_OutImport.Columns.Add(new DataColumn("Classification", typeof(string)));
            dt_OutImport.Columns.Add(new DataColumn("Index", typeof(string)));
            dt_OutImport.Columns.Add(new DataColumn("Name", typeof(string)));
            dt_OutImport.Columns.Add(new DataColumn("Description", typeof(string)));
            dt_OutImport.Columns.Add(new DataColumn("DataType", typeof(string)));
            dt_OutImport.Columns.Add(new DataColumn("RWProperty", typeof(string)));
            dt_OutImport.Columns.Add(new DataColumn("Unit", typeof(string)));
            dt_OutImport.Columns.Add(new DataColumn("Default", typeof(string)));
            dt_OutImport.Columns.Add(new DataColumn("Min", typeof(string)));
            dt_OutImport.Columns.Add(new DataColumn("Max", typeof(string)));
            dt_OutImport.Columns.Add(new DataColumn("Current", typeof(string)));
            dt_OutImport.Columns.Add(new DataColumn("Comment", typeof(string)));

            dt_OutDiff = dt_OutImport.Copy();
            dt_OutTempDiff = dt_OutImport.Copy();


            try
            {
                if (dt_DataImportTable == null)
                {
                    return RET.NO_EFFECT;
                }

                if (dt_DataImportTable.Rows.Count == 0)
                {
                    return RET.NO_EFFECT;
                }

                arrBaseClassification = dt_DataBaseTable.AsEnumerable().Select(t => t.Field<string>("Classification")).Distinct().ToArray();
                arrImportClassification = dt_DataImportTable.AsEnumerable().Select(t => t.Field<string>("Classification")).Distinct().ToArray();

                foreach (string Condition in arrImportClassification)
                {
                    if (Condition == "轴共同参数" || Condition == "监控参数" || Condition == "辅助参数" || Condition == "ServoStudio")
                    {
                        continue;
                    }

                    strCondition = ("\" Classification = '" + Condition + "'\"").Replace("\"", "");

                    arrBaseDataRows = dt_DataBaseTable.Select(strCondition);
                    arrImportDataRows = dt_DataImportTable.Select(strCondition);

                    if (arrBaseDataRows.Length >= arrImportDataRows.Length)
                    {
                        arrMaxDataRows = arrBaseDataRows;
                        rowMaxCount = arrBaseDataRows.Length;
                        rowMinCount = arrImportDataRows.Length;
                    }
                    else
                    {
                        arrMaxDataRows = arrImportDataRows;
                        rowMaxCount = arrImportDataRows.Length;
                        rowMinCount = arrBaseDataRows.Length;
                    }

                    for (int iRowValue = 0; iRowValue < arrMaxDataRows.Length; iRowValue++)
                    {
                        bEmpty = true;

                        DataRow dr_OutImpot = dt_OutImport.NewRow();
                        DataRow dr_OutTempDiff = dt_OutTempDiff.NewRow();
                        DataRow dr_OutDiff = dt_OutDiff.NewRow();

                        if (arrBaseDataRows.Length >= arrImportDataRows.Length)
                        {
                            columnCount = arrBaseDataRows[iRowValue].ItemArray.Length;
                        }
                        else
                        {
                            columnCount = arrImportDataRows[iRowValue].ItemArray.Length;
                        }

                        #region 导入参数文件与基准参数文件行数相同
                        if (iRowValue < rowMinCount)
                        {
                            //轮询每一列信息，一共13列，相同行数
                            for (int iColumnValue = 0; iColumnValue < columnCount; iColumnValue++)
                            {
                                #region 只读属性或时间戳跳过
                                if (Convert.ToString(arrImportDataRows[iRowValue][6]).ToUpper() == "RO" || Convert.ToString(arrImportDataRows[iRowValue][3]) == "Timestamp")
                                {
                                    continue;
                                }
                                #endregion

                                #region 其他行的其他属性
                                if (Convert.ToString(arrImportDataRows[iRowValue][iColumnValue]) == Convert.ToString(arrBaseDataRows[iRowValue][iColumnValue]))
                                {
                                    if (arrImportDataRows[iRowValue][iColumnValue] == null)
                                    {
                                        dr_OutImpot[iColumnValue] = null;
                                        continue;
                                    }
                                    if (Convert.ToString(arrImportDataRows[iRowValue][iColumnValue]) == "")
                                    {
                                        dr_OutImpot[iColumnValue] = null;
                                    }
                                    else
                                    {
                                        dr_OutImpot[iColumnValue] = arrImportDataRows[iRowValue][iColumnValue].ToString().Trim();

                                        bEmpty = false;
                                    }
                                }
                                else
                                {
                                    if (arrImportDataRows[iRowValue][iColumnValue] == null)
                                    {
                                        dr_OutTempDiff[iColumnValue] = null;
                                        continue;
                                    }
                                    if (Convert.ToString(arrImportDataRows[iRowValue][iColumnValue]) == "")
                                    {
                                        dr_OutTempDiff[iColumnValue] = null;
                                    }
                                    else
                                    {
                                        dr_OutTempDiff[iColumnValue] = arrImportDataRows[iRowValue][iColumnValue].ToString().Trim();

                                        bEmpty = false;
                                        isDiff = true;
                                        bDiff = true;
                                    }
                                }
                                #endregion
                            }
                        }
                        #endregion

                        #region 导入参数文件多出行数内容信息
                        if (iRowValue >= arrBaseDataRows.Length && iRowValue <= rowMaxCount)
                        {
                            //轮询每一列信息，一共13列，相同行数
                            for (int iColumnValue = 0; iColumnValue < columnCount; iColumnValue++)
                            {
                                #region 只读属性或时间戳跳过
                                if (Convert.ToString(arrImportDataRows[iRowValue][6]).ToUpper() == "RO" || Convert.ToString(arrImportDataRows[iRowValue][3]) == "Timestamp")
                                {
                                    continue;
                                }
                                #endregion

                                #region 其他行的其他属性
                                if (arrImportDataRows[iRowValue][iColumnValue] == null)
                                {
                                    dr_OutTempDiff[iColumnValue] = null;
                                    continue;
                                }
                                if (Convert.ToString(arrImportDataRows[iRowValue][iColumnValue]) == "")
                                {
                                    dr_OutTempDiff[iColumnValue] = null;
                                }
                                else
                                {
                                    dr_OutTempDiff[iColumnValue] = arrImportDataRows[iRowValue][iColumnValue].ToString().Trim();

                                    bEmpty = false;
                                    isDiff = true;
                                    bDiff = true;
                                }
                                #endregion
                            }
                        }
                        #endregion

                        #region 基准参数文件多出行数内容信息
                        if (iRowValue >= arrImportDataRows.Length && iRowValue <= rowMaxCount)
                        {
                            //轮询每一列信息，一共13列，相同行数
                            for (int iColumnValue = 0; iColumnValue < columnCount; iColumnValue++)
                            {
                                #region 只读属性或时间戳跳过
                                if (Convert.ToString(arrBaseDataRows[iRowValue][6]).ToUpper() == "RO" || Convert.ToString(arrBaseDataRows[iRowValue][3]) == "Timestamp")
                                {
                                    continue;
                                }
                                #endregion

                                #region 其他行的其他属性
                                if (arrBaseDataRows[iRowValue][iColumnValue] == null)
                                {
                                    dr_OutTempDiff[iColumnValue] = null;
                                    continue;
                                }
                                if (Convert.ToString(arrBaseDataRows[iRowValue][iColumnValue]) == "")
                                {
                                    dr_OutTempDiff[iColumnValue] = null;
                                }
                                else
                                {
                                    dr_OutTempDiff[iColumnValue] = arrBaseDataRows[iRowValue][iColumnValue].ToString().Trim();

                                    bEmpty = false;
                                    isDiff = true;
                                    bDiff = true;
                                }
                                #endregion
                            }
                        }
                        #endregion

                        //如果有不同项，把整行信息添加到差异行中
                        if (isDiff)
                        {
                            //导入文件和基准文件行数相同
                            if (iRowValue < rowMinCount)
                            {
                                for (int iColumnValue = 0; iColumnValue < columnCount; iColumnValue++)
                                {
                                    if (Convert.ToString(dr_OutTempDiff[iColumnValue]) == "")
                                    {
                                        dr_OutDiff[iColumnValue] = arrImportDataRows[iRowValue][iColumnValue].ToString().Trim();
                                    }
                                    else
                                    {
                                        dr_OutDiff[iColumnValue] = arrBaseDataRows[iRowValue][iColumnValue].ToString().Trim() + "\r\n" + "导入: " + arrImportDataRows[iRowValue][iColumnValue].ToString().Trim();

                                        string a = dr_OutDiff[iColumnValue].ToString();
                                        int b = a.Length;
                                        bool c = a.Contains("导入: ");
                                    }
                                }

                                isDiff = false;
                            }
                            //导入文件多出的行信息
                            if (iRowValue >= arrBaseDataRows.Length && iRowValue <= rowMaxCount)
                            {
                                for (int iColumnValue = 0; iColumnValue < columnCount; iColumnValue++)
                                {
                                    dr_OutDiff[iColumnValue] = "导入: " + arrImportDataRows[iRowValue][iColumnValue].ToString().Trim();
                                }

                                isDiff = false;
                            }
                            //基准文件多出的行信息
                            if (iRowValue >= arrImportDataRows.Length && iRowValue <= rowMaxCount)
                            {
                                for (int iColumnValue = 0; iColumnValue < columnCount; iColumnValue++)
                                {
                                    dr_OutDiff[iColumnValue] = "导入: " + arrBaseDataRows[iRowValue][iColumnValue].ToString().Trim();
                                }

                                isDiff = false;
                            }
                        }

                        if (bEmpty == false)
                        {
                            if (bDiff)
                            {
                                dt_OutDiff.Rows.Add(dr_OutDiff);

                                bDiff = false;
                            }
                            else
                            {
                                dt_OutImport.Rows.Add(dr_OutImpot);
                            }
                        }

                    }
                }

                Out_clsDataDiffTable = dt_OutDiff.Copy();
                Out_clsDataImportTable = dt_OutImport.Copy();

                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OTHERSHELPER_EXCEL_FOR_COMPARE, "DataTableExportUpdate", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：DataTableUpdate
        //函数功能：GlobalParameterSet.dt_Export数据更新
        //
        //输入参数：NONE    
        //
        //输出参数：NONE
        //        
        //编码作者：Ryan
        //更新时间：2020.01.17
        //*************************************************************************
        public static int DataTableExportUpdate(ref DataTable dt)
        {
            string strCondition = null;
            string strValue = null;
            DataRow[] rows_Export = null;
            int iIndex_Export = 0;

            try
            {
                if (dt == null)
                {
                    return RET.NO_EFFECT;
                }

                if (dt.Rows.Count == 0)
                {
                    return RET.NO_EFFECT;
                }

                if (OthersHelper.SelectAxisNumber() == AxisNumber.A)
                {
                    foreach (string item in CommunicationSet.CurrentPointValue_AxisA.Keys.ToArray<string>())
                    {
                        //获取当前数值
                        CommunicationSet.CurrentPointValue_AxisA.TryGetValue(item, out strValue);

                        //检索条件
                        strCondition = ("\" Index = '" + "0x" + item + "'\"").Replace("\"", "");

                        //修改要导出的默认值
                        rows_Export = dt.Select(strCondition);
                        if (rows_Export.Length != 0)
                        {
                            iIndex_Export = dt.Rows.IndexOf(rows_Export[0]);
                            dt.Rows[iIndex_Export]["Current"] = strValue;
                        }
                    }
                }

                if (OthersHelper.SelectAxisNumber() == AxisNumber.B)
                {
                    foreach (string item in CommunicationSet.CurrentPointValue_AxisB.Keys.ToArray<string>())
                    {
                        //获取当前数值                   
                        CommunicationSet.CurrentPointValue_AxisB.TryGetValue(item, out strValue);

                        //地址偏移还原
                        string strAddress = AddressRestore(item);

                        //检索条件
                        strCondition = ("\" Index = '" + "0x" + strAddress + "'\"").Replace("\"", "");

                        //修改要导出的默认值
                        rows_Export = dt.Select(strCondition);
                        if (rows_Export.Length != 0)
                        {
                            iIndex_Export = dt.Rows.IndexOf(rows_Export[0]);
                            dt.Rows[iIndex_Export]["Current"] = strValue;
                        }
                    }
                }

                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OTHERSHELPER_DATATABLE_EXPORT_UPDATE, "DataTableExportUpdate", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：DataTableExportUpdate_ForCompare
        //函数功能：修改DataTable的Cell值
        //
        //输入参数：NONE    
        //
        //输出参数：NONE
        //        
        //编码作者：Lilbert
        //更新时间：2023.05.19
        //*************************************************************************
        public static int DataTableExportUpdate_ForCompare(ref DataTable dt, string strItem, string strValue)
        {
            string strCondition = null;
            string item = null;
            DataRow[] rows_Export = null;
            int iIndex_Export = 0;

            try
            {
                if (dt == null)
                {
                    return RET.NO_EFFECT;
                }

                if (dt.Rows.Count == 0)
                {
                    return RET.NO_EFFECT;
                }

                item = strItem.Replace("0x", "");

                if (OthersHelper.SelectAxisNumber() == AxisNumber.A)
                {
                    //检索条件                 
                    strCondition = ("\" Index = '" + "0x" + item + "'\"").Replace("\"", "");

                    //修改要导出的默认值
                    rows_Export = dt.Select(strCondition);
                    if (rows_Export.Length != 0)
                    {
                        iIndex_Export = dt.Rows.IndexOf(rows_Export[0]);
                        dt.Rows[iIndex_Export]["Current"] = strValue;
                    }
                }

                if (OthersHelper.SelectAxisNumber() == AxisNumber.B)
                {
                    //地址偏移还原
                    string strAddress = AddressRestore(item);

                    //检索条件
                    strCondition = ("\" Index = '" + "0x" + strAddress + "'\"").Replace("\"", "");

                    //修改要导出的默认值
                    rows_Export = dt.Select(strCondition);
                    if (rows_Export.Length != 0)
                    {
                        iIndex_Export = dt.Rows.IndexOf(rows_Export[0]);
                        dt.Rows[iIndex_Export]["Current"] = strValue;
                    }
                }

                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OTHERSHELPER_DATATABLE_EXPORT_UPDATE, "DataTableExportUpdate", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：CheckTitleOfConfig
        //函数功能：配置文件的字段检测
        //
        //输入参数：NONE
        //
        //输出参数：0：NO_EFFECT
        //         1: OK
        //        -1: NG
        //        
        //编码作者：Ryan
        //更新时间：2019.12.09
        //*************************************************************************
        public static int CheckTitleOfConfig()
        {
            try
            {
                if (GlobalParameterSet.dt == null)
                    return RET.NO_EFFECT;

                if (GlobalParameterSet.dt.Rows.Count == 0)
                    return RET.NO_EFFECT;

                if (GlobalParameterSet.dt.Columns.IndexOf("Section") == -1)
                    return RET.NO_EFFECT;

                if (GlobalParameterSet.dt.Columns.IndexOf("Classification") == -1)
                    return RET.NO_EFFECT;

                if (GlobalParameterSet.dt.Columns.IndexOf("Index") == -1)
                    return RET.NO_EFFECT;

                if (GlobalParameterSet.dt.Columns.IndexOf("Name") == -1)
                    return RET.NO_EFFECT;

                if (GlobalParameterSet.dt.Columns.IndexOf("Description") == -1)
                    return RET.NO_EFFECT;

                if (GlobalParameterSet.dt.Columns.IndexOf("DataType") == -1)
                    return RET.NO_EFFECT;

                if (GlobalParameterSet.dt.Columns.IndexOf("RWProperty") == -1)
                    return RET.NO_EFFECT;

                if (GlobalParameterSet.dt.Columns.IndexOf("Unit") == -1)
                    return RET.NO_EFFECT;

                if (GlobalParameterSet.dt.Columns.IndexOf("Default") == -1)
                    return RET.NO_EFFECT;

                if (GlobalParameterSet.dt.Columns.IndexOf("Min") == -1)
                    return RET.NO_EFFECT;

                if (GlobalParameterSet.dt.Columns.IndexOf("Max") == -1)
                    return RET.NO_EFFECT;

                if (GlobalParameterSet.dt.Columns.IndexOf("Current") == -1)
                    return RET.NO_EFFECT;

                if (GlobalParameterSet.dt.Columns.IndexOf("Comment") == -1)
                    return RET.NO_EFFECT;

                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OTHERSHELPER_CHECK_TITLE_OF_CONFIG, "CheckTitleOfConfig", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：CheckTitleOfConfig_For_Compare
        //函数功能：配置文件的字段检测
        //
        //输入参数：NONE
        //
        //输出参数：0：NO_EFFECT
        //         1: OK
        //        -1: NG
        //        
        //编码作者：Lilbert
        //更新时间：2023.05.09
        //*************************************************************************
        public static int CheckTitleOfConfig_For_Compare()
        {
            try
            {
                if (GlobalParameterSet.dt_Import == null)
                    return RET.NO_EFFECT;

                if (GlobalParameterSet.dt_Import.Rows.Count == 0)
                    return RET.NO_EFFECT;

                if (GlobalParameterSet.dt_Import.Columns.IndexOf("Section") == -1)
                    return RET.NO_EFFECT;

                if (GlobalParameterSet.dt_Import.Columns.IndexOf("Classification") == -1)
                    return RET.NO_EFFECT;

                if (GlobalParameterSet.dt_Import.Columns.IndexOf("Index") == -1)
                    return RET.NO_EFFECT;

                if (GlobalParameterSet.dt_Import.Columns.IndexOf("Name") == -1)
                    return RET.NO_EFFECT;

                if (GlobalParameterSet.dt_Import.Columns.IndexOf("Description") == -1)
                    return RET.NO_EFFECT;

                if (GlobalParameterSet.dt_Import.Columns.IndexOf("DataType") == -1)
                    return RET.NO_EFFECT;

                if (GlobalParameterSet.dt_Import.Columns.IndexOf("RWProperty") == -1)
                    return RET.NO_EFFECT;

                if (GlobalParameterSet.dt_Import.Columns.IndexOf("Unit") == -1)
                    return RET.NO_EFFECT;

                if (GlobalParameterSet.dt_Import.Columns.IndexOf("Default") == -1)
                    return RET.NO_EFFECT;

                if (GlobalParameterSet.dt_Import.Columns.IndexOf("Min") == -1)
                    return RET.NO_EFFECT;

                if (GlobalParameterSet.dt_Import.Columns.IndexOf("Max") == -1)
                    return RET.NO_EFFECT;

                if (GlobalParameterSet.dt_Import.Columns.IndexOf("Current") == -1)
                    return RET.NO_EFFECT;

                if (GlobalParameterSet.dt_Import.Columns.IndexOf("Comment") == -1)
                    return RET.NO_EFFECT;

                //存在差异时需要字段检测，如果不存才差异时不需要字段检测
                if (GlobalParameterSet.dt_Diff == null && GlobalParameterSet.dt_Diff.Rows.Count == 0)
                {
                    return RET.SUCCEEDED;
                }
                else
                {
                    if (GlobalParameterSet.dt_Diff.Columns.IndexOf("Section") == -1)
                        return RET.NO_EFFECT;

                    if (GlobalParameterSet.dt_Diff.Columns.IndexOf("Classification") == -1)
                        return RET.NO_EFFECT;

                    if (GlobalParameterSet.dt_Diff.Columns.IndexOf("Index") == -1)
                        return RET.NO_EFFECT;

                    if (GlobalParameterSet.dt_Diff.Columns.IndexOf("Name") == -1)
                        return RET.NO_EFFECT;

                    if (GlobalParameterSet.dt_Diff.Columns.IndexOf("Description") == -1)
                        return RET.NO_EFFECT;

                    if (GlobalParameterSet.dt_Diff.Columns.IndexOf("DataType") == -1)
                        return RET.NO_EFFECT;

                    if (GlobalParameterSet.dt_Diff.Columns.IndexOf("RWProperty") == -1)
                        return RET.NO_EFFECT;

                    if (GlobalParameterSet.dt_Diff.Columns.IndexOf("Unit") == -1)
                        return RET.NO_EFFECT;

                    if (GlobalParameterSet.dt_Diff.Columns.IndexOf("Default") == -1)
                        return RET.NO_EFFECT;

                    if (GlobalParameterSet.dt_Diff.Columns.IndexOf("Min") == -1)
                        return RET.NO_EFFECT;

                    if (GlobalParameterSet.dt_Diff.Columns.IndexOf("Max") == -1)
                        return RET.NO_EFFECT;

                    if (GlobalParameterSet.dt_Diff.Columns.IndexOf("Current") == -1)
                        return RET.NO_EFFECT;

                    if (GlobalParameterSet.dt_Diff.Columns.IndexOf("Comment") == -1)
                        return RET.NO_EFFECT;
                }

                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OTHERSHELPER_CHECK_TITLE_OF_CONFIG, "CheckTitleOfConfig", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：RefreshDictionary
        //函数功能：更新字典
        //
        //输入参数：NONE
        //
        //输出参数：0：NO_EFFECT
        //         1: OK
        //        -1: NG
        //        
        //编码作者：Ryan
        //更新时间：2019.12.09
        //*************************************************************************
        public static int RefreshDictionary(DataTable dt, ref Dictionary<string, string> dicA, ref Dictionary<string, string> dicB)
        {
            string strKey_AxisA = null;
            string strKey_AxisB = null;
            string strValue = null;

            try
            {
                if (dt == null)
                {
                    return RET.NO_EFFECT;
                }

                dicA = new Dictionary<string, string>();
                dicB = new Dictionary<string, string>();

                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    strValue = Convert.ToString(dt.Rows[i]["Default"]);

                    strKey_AxisA = Convert.ToString(dt.Rows[i]["Index"]).Replace("0x", "").Replace(" ", "").Replace("-", "");
                    if (string.IsNullOrEmpty(strKey_AxisA))
                    {
                        continue;
                    }

                    strKey_AxisB = AddressDeviation(strKey_AxisA);
                    if (string.IsNullOrEmpty(strKey_AxisB))
                    {
                        continue;
                    }

                    if (!dicA.ContainsKey(strKey_AxisA))
                    {
                        dicA.Add(strKey_AxisA, strValue);
                    }

                    if (!dicB.ContainsKey(strKey_AxisB))
                    {
                        dicB.Add(strKey_AxisB, strValue);
                    }
                }

                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OTHERSHELPER_REFRESH_DICTIONARY, "RefreshDictionary", ex);
                return RET.ERROR;
            }
        }

        public static int RefreshDictionary(DataTable dt, ref List<AlarmInfoSet> lst)
        {
            lst = new List<AlarmInfoSet>();

            try
            {
                if (dt == null)
                {
                    return RET.NO_EFFECT;
                }

                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    AlarmInfoSet clsAlarmInfoSet = new AlarmInfoSet();
                    clsAlarmInfoSet.Bit = Convert.ToString(dt.Rows[i]["Bit"]);
                    clsAlarmInfoSet.Code = Convert.ToString(dt.Rows[i]["Code"]);
                    clsAlarmInfoSet.Level = Convert.ToString(dt.Rows[i]["Level"]);
                    clsAlarmInfoSet.Content = Convert.ToString(dt.Rows[i]["Content"]);
                    clsAlarmInfoSet.Reason = Convert.ToString(dt.Rows[i]["Reason"]);
                    clsAlarmInfoSet.Measure = Convert.ToString(dt.Rows[i]["Measure"]);

                    lst.Add(clsAlarmInfoSet);
                }

                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OTHERSHELPER_REFRESH_DICTIONARY, "RefreshDictionary", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：RefreshAcquisitionInfoSet
        //函数功能：更新数采信息集合
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.12.23
        //*************************************************************************
        public static void RefreshAcquisitionInfoSet()
        {
            AcquisitionInfoSet.IsExistTask = true;
            AcquisitionInfoSet.CurrentProcess = TaskName.AssigningAcquisition;

            AcquisitionInfoSet.AcquisitionSwitch = true;
            AcquisitionInfoSet.ChannelNumberOfCurrentMessage = 0;
            AcquisitionInfoSet.CurrentMessageNumber = 0;

            if (ViewModelSet.Oscilloscope?.SelectedContinuousSampling == "是")
                AcquisitionInfoSet.IsContinuous = true;
            else
                AcquisitionInfoSet.IsContinuous = false;

            if (ViewModelSet.Oscilloscope?.SelectedDisplayMethod == "连续采样-静态展示")
                AcquisitionInfoSet.OscilloscopeDisplayMethod = OscilloscopeDisplayMethod.STATIC;
            else
                AcquisitionInfoSet.OscilloscopeDisplayMethod = OscilloscopeDisplayMethod.DYNAMIC;
        }

        //*************************************************************************
        //函数名称：RefreshFaultAcquisitionInfoSet
        //函数功能：更新故障数据采信息集合
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.04.03
        //*************************************************************************
        public static void RefreshFaultAcquisitionInfoSet()
        {
            FaultAcquisitionInfoSet.IsExistTask = true;
            FaultAcquisitionInfoSet.CurrentProcess = TaskName.AssigningFaultAcquisition;

            FaultAcquisitionInfoSet.AcquisitionSwitch = true;
            FaultAcquisitionInfoSet.ChannelNumberOfCurrentMessage = 0;
            FaultAcquisitionInfoSet.CurrentMessageNumber = 0;

            //if (ViewModelSet.FaultDataOscilloscope?.SelectedContinuousSampling == "是")
            //    FaultAcquisitionInfoSet.IsContinuous = true;
            //else
            //    FaultAcquisitionInfoSet.IsContinuous = false;

            if (ViewModelSet.FaultDataOscilloscope?.SelectedDisplayMethod == "连续采样-静态展示")
                FaultAcquisitionInfoSet.OscilloscopeDisplayMethod = FaultDataOscilloscopeDisplayMethod.STATIC;
            else
                FaultAcquisitionInfoSet.OscilloscopeDisplayMethod = FaultDataOscilloscopeDisplayMethod.DYNAMIC;
        }

        //*************************************************************************
        //函数名称：ClearAcquisitionInfoSet
        //函数功能：清除示波器采样信息集合
        //
        //输入参数：NONE
        //
        //输出参数：0：NO_EFFECT
        //         1: OK
        //        -1: NG
        //        
        //编码作者：Ryan
        //更新时间：2019.12.09
        //*************************************************************************
        public static void ClearAcquisitionInfoSet()
        {
            AcquisitionInfoSet.IsExistTask = false;
            AcquisitionInfoSet.CurrentProcess = TaskName.Empty;
        }

        //*************************************************************************
        //函数名称：ClearAcquisitionInfoSet
        //函数功能：清除示波器采样信息集合
        //
        //输入参数：NONE
        //
        //输出参数：0：NO_EFFECT
        //         1: OK
        //        -1: NG
        //        
        //编码作者：Lilbert
        //更新时间：2023.04.03
        //*************************************************************************
        public static void ClearFaultAcquisitionInfoSet()
        {
            FaultAcquisitionInfoSet.IsExistTask = false;
            FaultAcquisitionInfoSet.CurrentProcess = TaskName.Empty;
        }

        //*************************************************************************
        //函数名称：ClearControlWordSet
        //函数功能：清除控制字集合
        //
        //输入参数：NONE
        //
        //输出参数：NONE
        //        
        //编码作者：Ryan
        //更新时间：2020.03.20
        //*************************************************************************
        public static void ClearControlWordSet()
        {
            ControlWordSet.WriteSwitch = false;
            ControlWordSet.IndexOfList = 0;
            ControlWordSet.TaskName = null;
            ControlWordSet.IsExistTaskAfterControlWord = false;

            if (ControlWordSet.ListValue == null)
            {
                ControlWordSet.ListValue = new List<int>();
            }
            else
            {
                ControlWordSet.ListValue.Clear();
            }
        }

        //*************************************************************************
        //函数名称：ClearAllAcquisitionTask
        //函数功能：清除所有采样任务
        //
        //输入参数：NONE
        //         
        //输出参数：1：SUCCEED    
        //        -1: ERROR
        //        
        //编码作者：Ryan
        //更新时间：2019.12.26
        //*************************************************************************
        public static int ClearAllAcquisitionTask()
        {
            try
            {
                if (SerialPortTask.TaskManagement == null)
                {
                    return RET.ERROR;
                }

                for (int i = 0; i < SerialPortTask.TaskManagement.Count;)
                {
                    if (SerialPortTask.TaskManagement[i].TaskName == TaskName.AssigningAcquisition || SerialPortTask.TaskManagement[i].TaskName == TaskName.AskingAcquisitionState || SerialPortTask.TaskManagement[i].TaskName == TaskName.UploadingAcquisition)
                    {
                        SerialPortTask.TaskManagement.RemoveAt(i);
                    }
                    else
                    {
                        i++;
                    }
                }

                ClearAcquisitionInfoSet();

                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                LogHelper.ErrorLog("ClearAllAcquisitionTask", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：ClearAllFaultAcquisitionTask
        //函数功能：清除所有故障数据采样任务
        //
        //输入参数：NONE
        //         
        //输出参数：1：SUCCEED    
        //        -1: ERROR
        //        
        //编码作者：Lilbert
        //更新时间：2023.04.03
        //*************************************************************************
        public static int ClearAllFaultAcquisitionTask()
        {
            try
            {
                if (SerialPortTask.TaskManagement == null)
                {
                    return RET.ERROR;
                }

                for (int i = 0; i < SerialPortTask.TaskManagement.Count;)
                {
                    if (SerialPortTask.TaskManagement[i].TaskName == TaskName.AssigningFaultAcquisition || SerialPortTask.TaskManagement[i].TaskName == TaskName.FaultAskingAcquisitionState || SerialPortTask.TaskManagement[i].TaskName == TaskName.UploadingAcquisitionFault)
                    {
                        SerialPortTask.TaskManagement.RemoveAt(i);
                    }
                    else
                    {
                        i++;
                    }
                }

                ClearFaultAcquisitionInfoSet();

                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                LogHelper.ErrorLog("ClearAllFaultAcquisitionTask", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：ClearFirmwareUpdateTask
        //函数功能：清除固件升级任务
        //
        //输入参数：NONE
        //         
        //输出参数：1：SUCCEED    
        //        -1: ERROR
        //        
        //编码作者：Ryan
        //更新时间：2019.12.26
        //*************************************************************************
        public static int ClearFirmwareUpdateTask()
        {
            try
            {
                if (SerialPortTask.TaskManagement == null)
                {
                    return RET.ERROR;
                }

                for (int i = 0; i < SerialPortTask.TaskManagement.Count;)
                {
                    if (SerialPortTask.TaskManagement[i].TaskName == TaskName.FirmwareUpdate)
                    {
                        SerialPortTask.TaskManagement.RemoveAt(i);
                    }
                    else
                    {
                        i++;
                    }
                }

                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                LogHelper.ErrorLog("ClearFirmwareUpdateTask", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：CloseAllThread
        //函数功能：关闭所有线程
        //
        //输入参数：NONE
        //         
        //输出参数：1：SUCCEED    
        //        -1: ERROR
        //        
        //编码作者：Ryan
        //更新时间：2019.12.26
        //*************************************************************************
        public static void CloseAllThread()
        {
            if (PthreadStatement.SerialPortTransmiting != null)
            {
                PthreadStatement.SerialPortTransmiting.PthreadSwitch = false;
            }

            if (PthreadStatement.MicrosecondsOscilloscopeDrawing != null)
            {
                PthreadStatement.MicrosecondsOscilloscopeDrawing.PthreadSwitch = false;
                PthreadStatement.MicrosecondsOscilloscopeDrawing.PthreadDispose = true;
            }
        }

        //*************************************************************************
        //函数名称：CheckIsAllThreadClosed
        //函数功能：判断是否关闭所有线程
        //
        //输入参数：NONE
        //         
        //输出参数：1：SUCCEED    
        //        -1: ERROR
        //        
        //编码作者：Ryan
        //更新时间：2019.12.26
        //*************************************************************************
        public static bool CheckIsAllThreadClosed()
        {
            if (!PthreadStatement.MicrosecondsOscilloscopeDrawing.PthreadWorking && !PthreadStatement.SerialPortTransmiting.PthreadWorking)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        //*************************************************************************
        //函数名称：IsInputNumber
        //函数功能：判断数据数字
        //
        //输入参数：NONE
        //         
        //输出参数：true: 正确
        //        false: 不正确
        //        
        //编码作者：Ryan
        //更新时间：2020.01.03
        //*************************************************************************
        public static bool IsInputNumber(string strInput)
        {
            if (string.IsNullOrEmpty(strInput))
            {
                return false;
            }

            if (Regex.IsMatch(strInput, @"^(-?\d+)(\.\d+)?$"))
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        //*************************************************************************
        //函数名称：IsInputInteger
        //函数功能：判断数据是否为整数
        //
        //输入参数：NONE
        //         
        //输出参数：true: 正确
        //        false: 不正确
        //        
        //编码作者：Ryan
        //更新时间：2020.01.03
        //*************************************************************************
        public static bool IsInputInteger(string strInput)
        {
            if (string.IsNullOrEmpty(strInput))
            {
                return false;
            }

            bool bRetA = false;
            bool bRetB = false;
            bRetA = Regex.IsMatch(strInput, @"^\d+$");
            bRetB = Regex.IsMatch(strInput, @"^((-\d+)|(0+))$");

            if (bRetA || bRetB)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        //*************************************************************************
        //函数名称：IsNumberAndWord
        //函数功能：判断数据是否为数字或为字母
        //
        //输入参数：NONE
        //         
        //输出参数：true: 正确
        //        false: 不正确
        //        
        //编码作者：Ryan
        //更新时间：2020.01.03
        //*************************************************************************
        public static bool IsNumberAndWord(string strValue)
        {
            if (string.IsNullOrEmpty(strValue))
            {
                return false;
            }

            Regex r = new Regex(@"^[a-fA-F0-9]+$");
            return r.Match(strValue).Success;
        }

        //*************************************************************************
        //函数名称：IsChineseCharacter
        //函数功能：判断数据是否有汉字
        //
        //输入参数：string strValue
        //         
        //输出参数：true: 正确
        //                 false: 不正确
        //        
        //编码作者：Ryan
        //更新时间：2020.08.06
        //*************************************************************************
        public static bool IsChineseCharacter(string strValue)
        {
            if (string.IsNullOrEmpty(strValue))
            {
                return false;
            }

            Regex reg = new Regex(@"[\u4e00-\u9fa5]");//正则表达式
            return reg.IsMatch(strValue);
        }

        //*************************************************************************
        //函数名称：IsOutOfRange
        //函数功能：是否在数据类型允许的范围内
        //
        //输入参数：string strInput      输入数据
        //         string strDataType   数据类型
        //         
        //输出参数：true: 在范围内 
        //        false: 不在范围内
        //        
        //编码作者：Ryan
        //更新时间：2020.01.03
        //*************************************************************************
        public static bool IsOutOfRange(ref string strInput, string strUnit, string strDataType, string strMax, string strMin)
        {
            Int64 iInput = 0;
            Int64 iMax = 0;
            Int64 iMin = 0;
            bool bIsDataBeExchanged = false;
            bool bIsModifyData = false;

            try
            {
                #region 初始状态判断
                if (string.IsNullOrEmpty(strInput) || string.IsNullOrEmpty(strDataType))
                {
                    return false;
                }

                if (!IsInputInteger(strInput))
                {
                    return false;
                }

                //参数转换到Int
                iInput = Convert.ToInt64(strInput);

                //是否单位换算过
                bIsDataBeExchanged = IsDataBeExchanged(strUnit);
                #endregion

                #region 是否超过数据类型的范围
                switch (strDataType.ToUpper())
                {
                    case "INT32":
                        iMax = 2147483647;
                        iMin = -2147483648;
                        break;
                    case "UINT32":
                        iMax = 4294967295;
                        iMin = 0;
                        break;
                    case "INT16":
                        iMax = 32767;
                        iMin = -32767;
                        break;
                    case "UINT16":
                        iMax = 65535;
                        iMin = 0;
                        break;
                    case "INT8":
                        iMax = 127;
                        iMin = -128;
                        break;
                    case "UINT8":
                        iMax = 255;
                        iMin = 0;
                        break;
                    default:
                        return false;
                }

                //超出范围
                if (iInput > iMax || iInput < iMin)
                {
                    if (bIsDataBeExchanged)//单位被换算
                    {
                        bIsModifyData = IsModifyData(iMax, iMin, ref iInput);
                        if (!bIsModifyData)
                        {
                            return false;
                        }
                    }
                    else//没有单位换算
                    {
                        return false;
                    }
                }
                #endregion

                #region 是否超过设定值得范围
                if (string.IsNullOrEmpty(strMax) || string.IsNullOrEmpty(strMin))
                {
                    return true;
                }

                if (CheckIsInputHex(strMax))
                {
                    switch (strDataType.ToUpper())
                    {
                        case "INT32":
                            iMax = Convert.ToInt32(strMax.Replace("0x", ""), 16);
                            break;
                        case "UINT32":
                            iMax = Convert.ToUInt32(strMax.Replace("0x", ""), 16);
                            break;
                        case "INT16":
                            iMax = Convert.ToInt16(strMax.Replace("0x", ""), 16);
                            break;
                        case "UINT16":
                            iMax = Convert.ToUInt16(strMax.Replace("0x", ""), 16);
                            break;
                        case "INT8":
                            iMax = Convert.ToSByte(strMax.Replace("0x", ""), 16);
                            break;
                        case "UINT8":
                            iMax = Convert.ToByte(strMax.Replace("0x", ""), 16);
                            break;
                        default:
                            return false;
                    }
                }
                else
                {
                    iMax = Convert.ToInt32(strMax);
                }

                if (CheckIsInputHex(strMin))
                {
                    switch (strDataType.ToUpper())
                    {
                        case "INT32":
                            iMin = Convert.ToInt32(strMin.Replace("0x", ""), 16);
                            break;
                        case "UINT32":
                            iMin = Convert.ToUInt32(strMin.Replace("0x", ""), 16);
                            break;
                        case "INT16":
                            iMin = Convert.ToInt16(strMin.Replace("0x", ""), 16);
                            break;
                        case "UINT16":
                            iMin = Convert.ToUInt16(strMin.Replace("0x", ""), 16);
                            break;
                        case "INT8":
                            iMin = Convert.ToSByte(strMin.Replace("0x", ""), 16);
                            break;
                        case "UINT8":
                            iMin = Convert.ToByte(strMin.Replace("0x", ""), 16);
                            break;
                        default:
                            return false;
                    }
                }
                else
                {
                    iMin = Convert.ToInt32(strMin);
                }

                if (iInput > iMax || iInput < iMin)
                {
                    if (bIsDataBeExchanged)//单位被换算
                    {
                        bIsModifyData = IsModifyData(iMax, iMin, ref iInput);
                        if (!bIsModifyData)
                        {
                            return false;
                        }
                    }
                    else//没有单位换算
                    {
                        return false;
                    }
                }
                #endregion

                return true;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OTHERSHELPER_IS_OUT_OF_RANGE, "IsOutOfRange" + strInput, ex);
                return false;
            }
            finally
            {
                strInput = Convert.ToString(iInput);
            }
        }

        //*************************************************************************
        //函数名称：IsModifyAfterDataExchange
        //函数功能：单位换算后可能超出最值，没有超出一定范围视为正常
        //
        //输入参数：string strPeriod     采样周期
        //         
        //输出参数：double temp          经过格式转换以后的采样周期
        //        
        //编码作者：Ryan
        //更新时间：2020.01.16
        //*************************************************************************
        public static bool IsModifyData(Int64 iMax, Int64 iMin, ref Int64 iInput)
        {
            bool bRet = false;
            Int64 iMaxDifference = Math.Abs(iInput - iMax);
            Int64 iMinDifference = Math.Abs(iInput - iMin);

            //判断更趋近于最大值还是最小值
            if (iMaxDifference < iMinDifference && iMaxDifference <= LimitValue.DataExchangeRedundancy)//趋近于最大值
            {
                iInput = iMax;
                bRet = true;
            }
            else if (iMaxDifference >= iMinDifference && iMinDifference <= LimitValue.DataExchangeRedundancy)//趋近于最小值
            {
                iInput = iMin;
                bRet = true;
            }
            else
            {
                bRet = false;
            }

            return bRet;
        }

        //*************************************************************************
        //函数名称： IsDataBeExchanged
        //函数功能：判断是否单位换算
        //
        //输入参数：string strUnit       当前字段单位
        //         
        //输出参数：true     参数被换算
        //                 false    参数没有被换算
        //        
        //编码作者：Ryan
        //更新时间：2020.11.24
        //*************************************************************************
        public static bool IsDataBeExchanged(string strUnit)
        {
            switch (strUnit)
            {
                case "cnt":
                    if (SelectUnit.Position == strUnit)
                    {
                        return false;
                    }
                    else
                    {
                        return true;
                    }
                case "cnt/s":
                    if (SelectUnit.Speed == strUnit)
                    {
                        return false;
                    }
                    else
                    {
                        return true;
                    }
                case "cnt/s^2":
                    if (SelectUnit.Acceleration == strUnit)
                    {
                        return false;
                    }
                    else
                    {
                        return true;
                    }
                case "0.1%额定扭矩":
                    if (SelectUnit.Torque == strUnit)
                    {
                        return false;
                    }
                    else
                    {
                        return true;
                    }
                case "指令单位":
                case "指令单位/s":
                case "指令单位/s2":
                case "编码器单位":
                    return true;
                default:
                    return false;
            }
        }

        //*************************************************************************
        //函数名称：GetAcquisitionPeriod
        //函数功能：获取采样周期-单位为毫秒
        //
        //输入参数：string strPeriod     采样周期
        //         
        //输出参数：double temp          经过格式转换以后的采样周期
        //        
        //编码作者：Ryan
        //更新时间：2020.01.16
        //*************************************************************************
        public static double GetAcquisitionPeriod(string strPeriod)
        {
            try
            {
                if (string.IsNullOrEmpty(strPeriod))
                {
                    return RET.NO_EFFECT;
                }

                if (strPeriod.IndexOf("μ") != -1)
                {
                    double temp = Convert.ToDouble(strPeriod.Replace("μs", ""));
                    temp = temp / 1000;

                    return temp;
                }
                else
                {
                    double temp = Convert.ToDouble(strPeriod.Replace("ms", ""));

                    return temp;
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OTHERSHELPER_GET_ACQUISITION_PERIOD, "GetAcquisitionPeriod", ex);
                return RET.NO_EFFECT;
            }
        }

        //*************************************************************************
        //函数名称：GetAcquisitionPeriod
        //函数功能：获取采样周期-单位为毫秒
        //
        //输入参数：string strPeriod     采样周期
        //         
        //输出参数：double temp          经过格式转换以后的采样周期
        //        
        //编码作者：Lilbert
        //更新时间：2023.04.12
        //*************************************************************************
        public static double GetAcquisitionPeriod_ForFaultData(string strPeriod)
        {
            try
            {
                if (string.IsNullOrEmpty(strPeriod))
                {
                    return RET.NO_EFFECT;
                }

                if (strPeriod.IndexOf("μ") != -1)
                {
                    double temp = Convert.ToDouble(strPeriod.Replace("μs", ""));
                    temp = temp / 1000;

                    return temp;
                }
                else
                {
                    double temp = Convert.ToDouble(strPeriod.Replace("ms", ""));

                    return temp;
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OTHERSHELPER_GET_ACQUISITION_PERIOD, "GetAcquisitionPeriod", ex);
                return RET.NO_EFFECT;
            }
        }

        //*************************************************************************
        //函数名称：DataTableToTransmitingDataInfoSet
        //函数功能：DataTable数据赋值到TransmitingDataInfoSet中
        //
        //输入参数：DataTable dt          
        //         ref List<List<TransmitingDataInfoSet>> lstTransmittingDataInfo   
        //
        //输出参数：0：NO_EFFECT
        //         1: OK
        //        -1: NG
        //        
        //编码作者：Ryan
        //更新时间：2020.02.11
        //*************************************************************************
        public static int DataTableToTransmitingDataInfoSet(DataTable dt, ref List<List<TransmitingDataInfoSet>> lstTransmittingDataInfo)
        {
            string strCondition = null;
            string[] arrClassification = null;
            string strContent = null;
            string strValue = null;
            DataRow[] arrDataRows = null;
            lstTransmittingDataInfo = new List<List<TransmitingDataInfoSet>>();
            List<TransmitingDataInfoSet> lstTemp = new List<TransmitingDataInfoSet>();

            try
            {
                if (dt == null)
                {
                    return RET.NO_EFFECT;
                }

                if (dt.Rows.Count == 0)
                {
                    return RET.NO_EFFECT;
                }

                arrClassification = dt.AsEnumerable().Select(t => t.Field<string>("Classification")).Distinct().ToArray();

                foreach (string Condition in arrClassification)
                {
                    if (Condition == "轴共同参数" || Condition == "监控参数" || Condition == "辅助参数" || Condition == "ServoStudio")
                    {
                        continue;
                    }

                    strCondition = ("\" Classification = '" + Condition + "'\"").Replace("\"", "");
                    arrDataRows = dt.Select(strCondition);

                    lstTemp = new List<TransmitingDataInfoSet>();
                    foreach (var item in arrDataRows)
                    {
                        #region 只读属性或时间戳跳过
                        if (item[6].ToString().ToUpper() == "RO" || item[3].ToString() == "Timestamp")
                        {
                            continue;
                        }
                        #endregion

                        #region Others
                        TransmitingDataInfoSet clsTemp = new TransmitingDataInfoSet();

                        if (OthersHelper.SelectAxisNumber() == AxisNumber.A)
                        {
                            clsTemp.Address = item[2].ToString().Replace("0x", "");
                        }
                        else
                        {
                            clsTemp.Address = OthersHelper.AddressDeviation(item[2].ToString().Replace("0x", ""));
                        }

                        clsTemp.DataType = item[5].ToString().ToUpper();
                        #endregion

                        #region Value
                        strValue = Convert.ToString(item[11]);

                        if (string.IsNullOrEmpty(strValue))
                        {
                            #region Current为空
                            strValue = Convert.ToString(item[8]);//如果Current为空，载入默认值

                            if (string.IsNullOrEmpty(strValue))//如果默认值为空，载入0
                            {
                                clsTemp.Value = "0";
                            }
                            else
                            {
                                if (strValue.Contains("0x"))//16进制
                                {
                                    strValue = strValue.Replace("0x", "");
                                    if (IsNumberAndWord(strValue))//判断是否都是数字或A-F
                                    {
                                        if (clsTemp.DataType == "INT32")
                                        {
                                            clsTemp.Value = Convert.ToInt32(strValue, 16).ToString();
                                        }
                                        else if (clsTemp.DataType == "UINT32")
                                        {
                                            clsTemp.Value = Convert.ToUInt32(strValue, 16).ToString();
                                        }
                                        else if (clsTemp.DataType == "INT16")
                                        {
                                            clsTemp.Value = Convert.ToInt16(strValue, 16).ToString();
                                        }
                                        else if (clsTemp.DataType == "UINT16")
                                        {
                                            clsTemp.Value = Convert.ToUInt16(strValue, 16).ToString();
                                        }
                                        else if (clsTemp.DataType == "INT8")
                                        {
                                            clsTemp.Value = Convert.ToSByte(strValue, 16).ToString();
                                        }
                                        else if (clsTemp.DataType == "UINT8")
                                        {
                                            clsTemp.Value = Convert.ToByte(strValue, 16).ToString();
                                        }
                                    }
                                    else
                                    {
                                        clsTemp.Value = "0";
                                    }
                                }
                                else
                                {
                                    if (IsInputInteger(strValue))
                                    {
                                        clsTemp.Value = strValue;
                                    }
                                    else
                                    {
                                        clsTemp.Value = "0";
                                    }
                                }
                            }
                            #endregion
                        }
                        else
                        {
                            #region Current不为空
                            if (strValue.Contains("0x"))//16进制
                            {
                                strValue = strValue.Replace("0x", "");
                                if (IsNumberAndWord(strValue))//判断是否都是数字或A-F
                                {
                                    if (clsTemp.DataType == "INT32")
                                    {
                                        clsTemp.Value = Convert.ToInt32(strValue, 16).ToString();
                                    }
                                    else if (clsTemp.DataType == "UINT32")
                                    {
                                        clsTemp.Value = Convert.ToUInt32(strValue, 16).ToString();
                                    }
                                    else if (clsTemp.DataType == "INT16")
                                    {
                                        clsTemp.Value = Convert.ToInt16(strValue, 16).ToString();
                                    }
                                    else if (clsTemp.DataType == "UINT16")
                                    {
                                        clsTemp.Value = Convert.ToUInt16(strValue, 16).ToString();
                                    }
                                    else if (clsTemp.DataType == "INT8")
                                    {
                                        clsTemp.Value = Convert.ToSByte(strValue, 16).ToString();
                                    }
                                    else if (clsTemp.DataType == "UINT8")
                                    {
                                        clsTemp.Value = Convert.ToByte(strValue, 16).ToString();
                                    }
                                }
                                else
                                {
                                    clsTemp.Value = "0";
                                }
                            }
                            else
                            {
                                if (IsInputInteger(strValue))
                                {
                                    clsTemp.Value = strValue;
                                }
                                else
                                {
                                    clsTemp.Value = "0";
                                }
                            }
                            #endregion
                        }
                        #endregion

                        #region Content
                        if (clsTemp.DataType == "INT32")
                        {
                            strContent = clsTemp.Address + "20";
                            Int32 i32temp = Convert.ToInt32(clsTemp.Value);
                            strContent += HexHelper.ConvertHexStringEndian(BitConverter.ToString(BitConverter.GetBytes(i32temp)).Replace("-", ""));
                        }
                        else if (clsTemp.DataType == "UINT32")
                        {
                            strContent = clsTemp.Address + "20";
                            UInt32 u32temp = Convert.ToUInt32(clsTemp.Value);
                            strContent += HexHelper.ConvertHexStringEndian(BitConverter.ToString(BitConverter.GetBytes(u32temp)).Replace("-", ""));
                        }
                        else if (clsTemp.DataType == "INT16")
                        {
                            strContent = clsTemp.Address + "10";
                            Int16 i16temp = Convert.ToInt16(clsTemp.Value);
                            strContent += HexHelper.ConvertHexStringEndian(BitConverter.ToString(BitConverter.GetBytes(i16temp)).Replace("-", ""));
                        }
                        else if (clsTemp.DataType == "UINT16")
                        {
                            strContent = clsTemp.Address + "10";
                            UInt16 u16temp = Convert.ToUInt16(clsTemp.Value);
                            strContent += HexHelper.ConvertHexStringEndian(BitConverter.ToString(BitConverter.GetBytes(u16temp)).Replace("-", ""));
                        }
                        else if (clsTemp.DataType == "INT8")
                        {
                            strContent = clsTemp.Address + "08";
                            sbyte sb8temp = Convert.ToSByte(clsTemp.Value);
                            strContent += BitConverter.ToString(BitConverter.GetBytes(sb8temp)).Substring(0, 2);
                        }
                        else if (clsTemp.DataType == "UINT8")
                        {
                            strContent = clsTemp.Address + "08";
                            byte b8temp = Convert.ToByte(clsTemp.Value);
                            strContent += BitConverter.ToString(BitConverter.GetBytes(b8temp)).Substring(0, 2);
                        }

                        clsTemp.Content = strContent;
                        #endregion

                        lstTemp.Add(clsTemp);
                    }

                    lstTransmittingDataInfo.Add(lstTemp);
                }

                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OTHERSHELPER_DATATABLE_TO_TRANSMITING_DATA_INFO_SET, "DataTableToTransmitingDataInfoSet", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：DataTableToTransmitingDataInfoSet
        //函数功能：DataTable数据赋值到TransmitingDataInfoSet中
        //
        //输入参数：DataTable dt          
        //         ref List<List<TransmitingDataInfoSet>> lstTransmittingDataInfo   
        //
        //输出参数：0：NO_EFFECT
        //         1: OK
        //        -1: NG
        //        
        //编码作者：Lilberrt
        //更新时间：2023.05.16
        //*************************************************************************
        public static int DataTableToTransmitingDataInfoSet_For_Compare(DataTable dt, ref List<List<TransmitingDataInfoSet>> lstTransmittingDataInfo)
        {
            string strCondition = null;
            string[] arrClassification = null;
            string strContent = null;
            string strValue = null;
            DataRow[] arrDataRows = null;
            lstTransmittingDataInfo = new List<List<TransmitingDataInfoSet>>();
            List<TransmitingDataInfoSet> lstTemp = new List<TransmitingDataInfoSet>();

            try
            {
                if (dt == null)
                {
                    return RET.NO_EFFECT;
                }

                if (dt.Rows.Count == 0)
                {
                    return RET.NO_EFFECT;
                }

                arrClassification = dt.AsEnumerable().Select(t => t.Field<string>("Classification")).Distinct().ToArray();

                foreach (string Condition in arrClassification)
                {
                    if (Condition == "轴共同参数" || Condition == "监控参数" || Condition == "辅助参数" || Condition == "ServoStudio")
                    {
                        continue;
                    }

                    strCondition = ("\" Classification = '" + Condition + "'\"").Replace("\"", "");
                    arrDataRows = dt.Select(strCondition);

                    lstTemp = new List<TransmitingDataInfoSet>();
                    foreach (var item in arrDataRows)
                    {
                        #region 某行中都含有特定字符时跳过
                        if (item[0].ToString().Contains("导入: ") && item[1].ToString().Contains("导入: ")
                            && item[2].ToString().Contains("导入: ") && item[3].ToString().Contains("导入: ")
                            && item[4].ToString().Contains("导入: ") && item[5].ToString().Contains("导入: ")
                            && item[6].ToString().Contains("导入: ") && item[7].ToString().Contains("导入: ")
                            && item[8].ToString().Contains("导入: ") && item[9].ToString().Contains("导入: ")
                            && item[10].ToString().Contains("导入: ") && item[11].ToString().Contains("导入: ")
                            && item[12].ToString().Contains("导入: "))
                        {
                            continue;
                        }
                        #endregion

                        #region 只读属性或时间戳跳过
                        if (item[6].ToString().Contains("导入: "))
                        {
                            if (RemoveSubstring(item[6].ToString()).ToUpper() == "RO")
                            {
                                continue;
                            }
                        }
                        if (item[3].ToString().Contains("导入: "))
                        {
                            if (RemoveSubstring(item[3].ToString()) == "Timestamp")
                            {
                                continue;
                            }
                        }
                        //if (item[6].ToString().Contains("导入: ") || item[3].ToString().Contains("导入: "))
                        //{
                        //    if (RemoveSubstring(item[6].ToString()).ToUpper() == "RO" || RemoveSubstring(item[3].ToString()) == "Timestamp")
                        //    {
                        //        continue;
                        //    }
                        //}

                        #endregion

                        #region 索引号和数据类型
                        TransmitingDataInfoSet clsTemp = new TransmitingDataInfoSet();

                        if (OthersHelper.SelectAxisNumber() == AxisNumber.A)
                        {
                            if (item[2].ToString().Contains("导入: "))
                            {
                                clsTemp.Address = RemoveSubstring(item[2].ToString()).Replace("0x", "");
                            }
                            else
                            {
                                clsTemp.Address = item[2].ToString().Replace("0x", "");
                            }
                        }
                        else
                        {
                            if (item[2].ToString().Contains("导入: "))
                            {
                                clsTemp.Address = OthersHelper.AddressDeviation(RemoveSubstring(item[2].ToString()).Replace("0x", ""));
                            }
                            else
                            {
                                clsTemp.Address = OthersHelper.AddressDeviation(item[2].ToString().Replace("0x", ""));
                            }
                        }

                        //数据类型
                        if (item[5].ToString().Contains("导入: "))
                        {
                            clsTemp.DataType = RemoveSubstring(item[5].ToString()).ToUpper();
                        }
                        else
                        {
                            clsTemp.DataType = item[5].ToString().ToUpper();
                        }
                        #endregion

                        #region 当前参数
                        if (item[11].ToString().Contains("导入: "))
                        {
                            strValue = Convert.ToString(RemoveSubstring(item[11].ToString()));
                        }
                        else
                        {
                            strValue = Convert.ToString(item[11]);
                        }

                        if (string.IsNullOrEmpty(strValue))
                        {
                            #region Current为空
                            if (item[11].ToString().Contains("导入: "))
                            {
                                strValue = Convert.ToString(RemoveSubstring(item[8].ToString()));//如果Current为空，载入默认值
                            }
                            else
                            {
                                strValue = Convert.ToString(item[8]);//如果Current为空，载入默认值
                            }

                            if (string.IsNullOrEmpty(strValue))//如果默认值为空，载入0
                            {
                                clsTemp.Value = "0";
                            }
                            else
                            {
                                if (strValue.Contains("0x"))//16进制
                                {
                                    strValue = strValue.Replace("0x", "");
                                    if (IsNumberAndWord(strValue))//判断是否都是数字或A-F
                                    {
                                        if (clsTemp.DataType == "INT32")
                                        {
                                            clsTemp.Value = Convert.ToInt32(strValue, 16).ToString();
                                        }
                                        else if (clsTemp.DataType == "UINT32")
                                        {
                                            clsTemp.Value = Convert.ToUInt32(strValue, 16).ToString();
                                        }
                                        else if (clsTemp.DataType == "INT16")
                                        {
                                            clsTemp.Value = Convert.ToInt16(strValue, 16).ToString();
                                        }
                                        else if (clsTemp.DataType == "UINT16")
                                        {
                                            clsTemp.Value = Convert.ToUInt16(strValue, 16).ToString();
                                        }
                                        else if (clsTemp.DataType == "INT8")
                                        {
                                            clsTemp.Value = Convert.ToSByte(strValue, 16).ToString();
                                        }
                                        else if (clsTemp.DataType == "UINT8")
                                        {
                                            clsTemp.Value = Convert.ToByte(strValue, 16).ToString();
                                        }
                                    }
                                    else
                                    {
                                        clsTemp.Value = "0";
                                    }
                                }
                                else
                                {
                                    if (IsInputInteger(strValue))
                                    {
                                        clsTemp.Value = strValue;
                                    }
                                    else
                                    {
                                        clsTemp.Value = "0";
                                    }
                                }
                            }
                            #endregion
                        }
                        else
                        {
                            #region Current不为空
                            if (strValue.Contains("0x"))//16进制
                            {
                                strValue = strValue.Replace("0x", "");
                                if (IsNumberAndWord(strValue))//判断是否都是数字或A-F
                                {
                                    if (clsTemp.DataType == "INT32")
                                    {
                                        clsTemp.Value = Convert.ToInt32(strValue, 16).ToString();
                                    }
                                    else if (clsTemp.DataType == "UINT32")
                                    {
                                        clsTemp.Value = Convert.ToUInt32(strValue, 16).ToString();
                                    }
                                    else if (clsTemp.DataType == "INT16")
                                    {
                                        clsTemp.Value = Convert.ToInt16(strValue, 16).ToString();
                                    }
                                    else if (clsTemp.DataType == "UINT16")
                                    {
                                        clsTemp.Value = Convert.ToUInt16(strValue, 16).ToString();
                                    }
                                    else if (clsTemp.DataType == "INT8")
                                    {
                                        clsTemp.Value = Convert.ToSByte(strValue, 16).ToString();
                                    }
                                    else if (clsTemp.DataType == "UINT8")
                                    {
                                        clsTemp.Value = Convert.ToByte(strValue, 16).ToString();
                                    }
                                }
                                else
                                {
                                    clsTemp.Value = "0";
                                }
                            }
                            else
                            {
                                if (IsInputInteger(strValue))
                                {
                                    clsTemp.Value = strValue;
                                }
                                else
                                {
                                    clsTemp.Value = "0";
                                }
                            }
                            #endregion
                        }
                        #endregion

                        #region Content
                        if (clsTemp.DataType == "INT32")
                        {
                            strContent = clsTemp.Address + "20";
                            Int32 i32temp = Convert.ToInt32(clsTemp.Value);
                            strContent += HexHelper.ConvertHexStringEndian(BitConverter.ToString(BitConverter.GetBytes(i32temp)).Replace("-", ""));
                        }
                        else if (clsTemp.DataType == "UINT32")
                        {
                            strContent = clsTemp.Address + "20";
                            UInt32 u32temp = Convert.ToUInt32(clsTemp.Value);
                            strContent += HexHelper.ConvertHexStringEndian(BitConverter.ToString(BitConverter.GetBytes(u32temp)).Replace("-", ""));
                        }
                        else if (clsTemp.DataType == "INT16")
                        {
                            strContent = clsTemp.Address + "10";
                            Int16 i16temp = Convert.ToInt16(clsTemp.Value);
                            strContent += HexHelper.ConvertHexStringEndian(BitConverter.ToString(BitConverter.GetBytes(i16temp)).Replace("-", ""));
                        }
                        else if (clsTemp.DataType == "UINT16")
                        {
                            strContent = clsTemp.Address + "10";
                            UInt16 u16temp = Convert.ToUInt16(clsTemp.Value);
                            strContent += HexHelper.ConvertHexStringEndian(BitConverter.ToString(BitConverter.GetBytes(u16temp)).Replace("-", ""));
                        }
                        else if (clsTemp.DataType == "INT8")
                        {
                            strContent = clsTemp.Address + "08";
                            sbyte sb8temp = Convert.ToSByte(clsTemp.Value);
                            strContent += BitConverter.ToString(BitConverter.GetBytes(sb8temp)).Substring(0, 2);
                        }
                        else if (clsTemp.DataType == "UINT8")
                        {
                            strContent = clsTemp.Address + "08";
                            byte b8temp = Convert.ToByte(clsTemp.Value);
                            strContent += BitConverter.ToString(BitConverter.GetBytes(b8temp)).Substring(0, 2);
                        }

                        clsTemp.Content = strContent;
                        #endregion

                        lstTemp.Add(clsTemp);
                    }

                    lstTransmittingDataInfo.Add(lstTemp);
                }

                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OTHERSHELPER_DATATABLE_TO_TRANSMITING_DATA_INFO_SET, "DataTableToTransmitingDataInfoSet", ex);
                return RET.ERROR;
            }
        }

        public static int DataTableToReceivingDataInfoSet(DataTable dt, ref List<List<TransmitingDataInfoSet>> lstReceivingDataInfo)
        {
            string strCondition = null;
            string[] arrClassification = null;
            string strContent = null;
            DataRow[] arrDataRows = null;
            List<TransmitingDataInfoSet> lstTemp = null;
            lstReceivingDataInfo = new List<List<TransmitingDataInfoSet>>();

            try
            {
                if (dt == null)
                {
                    return RET.NO_EFFECT;
                }

                if (dt.Rows.Count == 0)
                {
                    return RET.NO_EFFECT;
                }

                arrClassification = dt.AsEnumerable().Select(t => t.Field<string>("Classification")).Distinct().ToArray();

                foreach (string Condition in arrClassification)
                {
                    if (Condition == "轴共同参数" || Condition == "监控参数" || Condition == "辅助参数" || Condition == "ServoStudio")
                    {
                        continue;
                    }

                    strCondition = ("\" Classification = '" + Condition + "'\"").Replace("\"", "");
                    arrDataRows = dt.Select(strCondition);

                    lstTemp = new List<TransmitingDataInfoSet>();
                    foreach (var item in arrDataRows)
                    {
                        #region Address&DataType
                        TransmitingDataInfoSet clsTemp = new TransmitingDataInfoSet();

                        if (OthersHelper.SelectAxisNumber() == AxisNumber.A)
                        {
                            clsTemp.Address = item[2].ToString().Replace("0x", "");
                        }
                        else
                        {
                            clsTemp.Address = OthersHelper.AddressDeviation(item[2].ToString().Replace("0x", ""));
                        }

                        clsTemp.DataType = item[5].ToString().ToUpper();
                        #endregion

                        #region Content
                        if (clsTemp.DataType == "INT32")
                        {
                            strContent = clsTemp.Address + "20";
                        }
                        else if (clsTemp.DataType == "UINT32")
                        {
                            strContent = clsTemp.Address + "20";
                        }
                        else if (clsTemp.DataType == "INT16")
                        {
                            strContent = clsTemp.Address + "10";
                        }
                        else if (clsTemp.DataType == "UINT16")
                        {
                            strContent = clsTemp.Address + "10";
                        }
                        else if (clsTemp.DataType == "INT8")
                        {
                            strContent = clsTemp.Address + "08";
                        }
                        else if (clsTemp.DataType == "UINT8")
                        {
                            strContent = clsTemp.Address + "08";
                        }

                        clsTemp.Content = strContent;
                        #endregion

                        lstTemp.Add(clsTemp);
                    }

                    lstReceivingDataInfo.Add(lstTemp);
                }

                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OTHERSHELPER_DATATABLE_TO_TRANSMITING_DATA_INFO_SET, "DataTableToTransmitingDataInfoSet", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：AddressDeviation
        //函数功能：地址偏移
        //
        //输入参数：string strAddress    地址
        //
        //输出参数：string strAddress    偏移地址
        //        
        //编码作者：Ryan
        //更新时间：2020.02.24
        //*************************************************************************
        public static string AddressDeviation(string strAddress)
        {
            int iAddress = 0;
            string strAddressDeviation = null;

            try
            {
                if (string.IsNullOrEmpty(strAddress)) return null;
                if (strAddress.Length != 6) return null;

                iAddress = Convert.ToInt32(strAddress, 16);
                iAddress += 524288;

                strAddressDeviation = HexHelper.ConvertHexStringEndian(BitConverter.ToString(BitConverter.GetBytes(iAddress)).Replace("-", "")).Remove(0, 2);
                return strAddressDeviation;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OTHERSHELPER_ADDRESS_DEVIATION, "AddressDeviation", ex);
                return null;
            }
        }

        //*************************************************************************
        //函数名称：RemoveSubstring
        //函数功能：去掉子字符串
        //
        //输入参数：string strItem    字符串
        //
        //输出参数：string strAddress    偏移地址
        //        
        //编码作者：Lilbert
        //更新时间：2023.05.17
        //*************************************************************************
        public static string RemoveSubstring(string strItem)
        {
            int strLength = 0;
            string strSubstring = null;

            try
            {
                if (string.IsNullOrEmpty(strItem)) return null;
                //if (strItem.Contains("RW")) return strItem;

                strLength = strItem.IndexOf(" ");
                string a = strItem.Substring(strLength + 1, strItem.Length - strLength - 1);
                int b = a.Length - 1;
                strSubstring = strItem.Substring(strLength, strItem.Length - strLength).Substring(1, strItem.Substring(strLength, strItem.Length - strLength).Length - 1);

                return strSubstring;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OTHERSHELPER_REMOVE_SUBSTRING, "RemoveSubstring", ex);
                return null;
            }
        }

        //*************************************************************************
        //函数名称：AddressRestore
        //函数功能：地址偏移还原
        //
        //输入参数：string strAddress    地址
        //
        //输出参数：string strAddress    偏移地址
        //        
        //编码作者：Ryan
        //更新时间：2020.02.24
        //*************************************************************************
        public static string AddressRestore(string strAddress)
        {
            int iAddress = 0;
            string strAddressRestore = null;

            try
            {
                if (string.IsNullOrEmpty(strAddress)) return null;
                if (strAddress.Length != 6) return null;

                iAddress = Convert.ToInt32(strAddress, 16);
                iAddress -= 524288;

                strAddressRestore = HexHelper.ConvertHexStringEndian(BitConverter.ToString(BitConverter.GetBytes(iAddress)).Replace("-", "")).Remove(0, 2);
                return strAddressRestore;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OTHERSHELPER_ADDRESS_RESTORE, "AddressRestore", ex);
                return null;
            }
        }

        //*************************************************************************
        //函数名称：SelectAxisNumber
        //函数功能：选取轴
        //
        //输入参数：None
        //
        //输出参数：int Axis     轴编号
        //        
        //编码作者：Ryan
        //更新时间：2020.02.24
        //*************************************************************************
        public static int SelectAxisNumber()
        {
            if (SoftwareStateParameterSet.AxisID == "00")
            {
                return AxisNumber.A;
            }
            else
            {
                return AxisNumber.B;
            }
        }

        //*************************************************************************
        //函数名称：GetParameterWriteSet
        //函数功能：用于参数写
        //
        //输入参数：Dictionary<string,string> dicParameterInfo       写参数名称和对应的值
        //         List<ParameterReadWriteSet> lstParameterInfo     写参数集合
        //
        //输出参数：-1：ERROR
        //          0：NO_EFFECT
        //          1：OK
        //        
        //编码作者：Ryan
        //更新时间：2020.02.29
        //*************************************************************************
        public static int GetParameterForWrite(Dictionary<string, string> dicParameterInfo, ref List<ParameterReadWriteSet> lstParameterInfo)
        {
            string strValue = null;
            lstParameterInfo = new List<ParameterReadWriteSet>();

            try
            {
                if (dicParameterInfo == null)
                {
                    return RET.NO_EFFECT;
                }

                if (dicParameterInfo.Count == 0)
                {
                    return RET.NO_EFFECT;
                }

                foreach (string Name in dicParameterInfo.Keys)
                {
                    dicParameterInfo.TryGetValue(Name, out strValue);

                    ParameterReadWriteSet clsParameterInfoSet = new ParameterReadWriteSet();
                    clsParameterInfoSet.Classification = GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", Name, "Classification");
                    clsParameterInfoSet.DataType = GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", Name, "DataType");
                    clsParameterInfoSet.Description = GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", Name, "Description");
                    clsParameterInfoSet.Index = GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", Name, "Index");
                    clsParameterInfoSet.Max = GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", Name, "Max");
                    clsParameterInfoSet.Min = GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", Name, "Min");
                    clsParameterInfoSet.RWProperty = GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", Name, "RWProperty");
                    clsParameterInfoSet.Unit = GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", Name, "Unit");
                    clsParameterInfoSet.CurrentValue = strValue;
                    clsParameterInfoSet.Name = Name;

                    if (clsParameterInfoSet.Index == "0")//配置文件里没有这个值
                    {
                        continue;
                    }

                    if (string.IsNullOrEmpty(clsParameterInfoSet.Unit))
                    {
                        return RET.NO_EFFECT;
                    }

                    if (string.IsNullOrEmpty(clsParameterInfoSet.Classification))
                    {
                        return RET.NO_EFFECT;
                    }

                    if (string.IsNullOrEmpty(clsParameterInfoSet.DataType))
                    {
                        return RET.NO_EFFECT;
                    }

                    if (string.IsNullOrEmpty(clsParameterInfoSet.Description))
                    {
                        return RET.NO_EFFECT;
                    }

                    if (string.IsNullOrEmpty(clsParameterInfoSet.Index))
                    {
                        return RET.NO_EFFECT;
                    }

                    //if (string.IsNullOrEmpty(clsParameterInfoSet.CurrentValue))
                    //{
                    //    return RET.NO_EFFECT;
                    //}

                    if (string.IsNullOrEmpty(clsParameterInfoSet.Name))
                    {
                        return RET.NO_EFFECT;
                    }

                    if (string.IsNullOrEmpty(clsParameterInfoSet.RWProperty))
                    {
                        return RET.NO_EFFECT;
                    }
                    else
                    {
                        if (clsParameterInfoSet.RWProperty.ToUpper() == "RO")
                        {
                            continue;
                        }
                    }

                    lstParameterInfo.Add(clsParameterInfoSet);
                }

                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OTHERSHELPER_GET_PARAMETER_READ_WRITE_SET, "GetParameterReadWriteSet", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：GetParameterForRead
        //函数功能：用于参数读
        //
        //输入参数：Dictionary<string,string> dicParameterInfo       写参数名称和对应的值
        //         List<ParameterReadWriteSet> lstParameterInfo     写参数集合
        //
        //输出参数：-1：ERROR
        //          0：NO_EFFECT
        //          1：OK
        //        
        //编码作者：Ryan
        //更新时间：2020.02.29
        //*************************************************************************
        public static int GetParameterForRead(Dictionary<string, string> dicParameterInfo, ref ObservableCollection<ParameterReadWriteSet> lstParameterInfo)
        {
            string strValue = null;
            lstParameterInfo = new ObservableCollection<ParameterReadWriteSet>();

            try
            {
                if (dicParameterInfo == null)
                {
                    return RET.NO_EFFECT;
                }

                if (dicParameterInfo.Count == 0)
                {
                    return RET.NO_EFFECT;
                }

                foreach (string Name in dicParameterInfo.Keys)
                {
                    dicParameterInfo.TryGetValue(Name, out strValue);

                    ParameterReadWriteSet clsParameterReadSet = new ParameterReadWriteSet();
                    clsParameterReadSet.Classification = GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", Name, "Classification");
                    clsParameterReadSet.DataType = GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", Name, "DataType");
                    clsParameterReadSet.Description = GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", Name, "Description");
                    clsParameterReadSet.Index = GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", Name, "Index");
                    clsParameterReadSet.Unit = GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", Name, "Unit");
                    clsParameterReadSet.CurrentValue = strValue;
                    clsParameterReadSet.Name = Name;

                    if (clsParameterReadSet.Index == "0")//配置文件里没有这个值
                    {
                        continue;
                    }

                    if (string.IsNullOrEmpty(clsParameterReadSet.Classification))
                    {
                        return RET.NO_EFFECT;
                    }

                    if (string.IsNullOrEmpty(clsParameterReadSet.DataType))
                    {
                        return RET.NO_EFFECT;
                    }

                    if (string.IsNullOrEmpty(clsParameterReadSet.Description))
                    {
                        return RET.NO_EFFECT;
                    }

                    if (string.IsNullOrEmpty(clsParameterReadSet.Index))
                    {
                        return RET.NO_EFFECT;
                    }

                    lstParameterInfo.Add(clsParameterReadSet);
                }

                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OTHERSHELPER_GET_PARAMETER_READ_WRITE_SET, "GetParameterReadWriteSet", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：GetCurrentValueOfIndex
        //函数功能：获取地址的当前值
        //
        //输入参数：string strIndex  地址
        //         
        //输出参数：string strCurrentValue   当前值
        //        
        //编码作者：Ryan
        //更新时间：2020.03.02
        //*************************************************************************
        public static string GetCurrentValueOfIndex(string strIndex)
        {
            string strValue = null;

            if (string.IsNullOrEmpty(strIndex))
            {
                return "0";
            }
            else
            {
                strIndex = strIndex.Replace("0x", "");
                if (OthersHelper.SelectAxisNumber() == AxisNumber.A)
                {
                    CommunicationSet.CurrentPointValue_AxisA.TryGetValue(strIndex, out strValue);
                }
                else
                {
                    strIndex = OthersHelper.AddressDeviation(strIndex);
                    if (string.IsNullOrEmpty(strIndex))
                    {
                        return "0";
                    }

                    CommunicationSet.CurrentPointValue_AxisB.TryGetValue(strIndex, out strValue);
                }

                if (string.IsNullOrEmpty(strValue))
                {
                    return "0";
                }

                if (!IsInputInteger(strValue))
                {
                    return "0";
                }
                else
                {
                    return strValue;
                }
            }
        }

        //*************************************************************************
        //函数名称：WriteControlWordSet
        //函数功能：写控制字集合
        //
        //输入参数：bool bWriteSwitch    写开关
        //         string strTaskName   任务名称
        //         
        //输出参数：-1：ERROR
        //          1: OK
        //          0：伺服状态异常
        //        
        //编码作者：Ryan&Lilbert
        //更新时间：2020.03.20&2021.06.15&2022.11.22
        //*************************************************************************
        public static int WriteControlWordSet(bool bWriteSwitch, int iStartIndex, string strTaskName)
        {
            try
            {
                //写入开关
                ControlWordSet.WriteSwitch = bWriteSwitch;

                //写入的检索号
                ControlWordSet.IndexOfList = iStartIndex;

                //写入的任务名称
                ControlWordSet.TaskName = strTaskName;

                //控制字后是否有新任务
                ControlWordSet.IsExistTaskAfterControlWord = false;

                //清空写数值集合
                if (ControlWordSet.ListValue == null)
                {
                    ControlWordSet.ListValue = new List<int>();
                }
                else
                {
                    ControlWordSet.ListValue.Clear();
                }

                if (strTaskName == TaskName.ServoEnabled)//伺服使能
                {
                    ControlWordSet.ListValue.Add(6);
                    ControlWordSet.ListValue.Add(7);
                    ControlWordSet.ListValue.Add(15);
                }
                else if (strTaskName == TaskName.AllAxisEnabled)    //由Lilbert添加else，解决伺服使能控件不能单独使能的问题
                {
                    ControlWordSet.ListValue.Add(6);
                    ControlWordSet.ListValue.Add(7);
                    ControlWordSet.ListValue.Add(15);

                    ControlWordSet.ListValue.Add(6);
                    ControlWordSet.ListValue.Add(7);
                    ControlWordSet.ListValue.Add(15);

                    ControlWordSet.ListValue.Add(6);
                    ControlWordSet.ListValue.Add(7);
                    ControlWordSet.ListValue.Add(15);

                    ControlWordSet.ListValue.Add(6);
                    ControlWordSet.ListValue.Add(7);
                    ControlWordSet.ListValue.Add(15);

                    ControlWordSet.ListValue.Add(6);
                    ControlWordSet.ListValue.Add(7);
                    ControlWordSet.ListValue.Add(15);

                    ControlWordSet.ListValue.Add(6);
                    ControlWordSet.ListValue.Add(7);
                    ControlWordSet.ListValue.Add(15);
                }
                else if (strTaskName == TaskName.ServoDisabled)//伺服禁能
                {
                    ControlWordSet.ListValue.Add(6);
                }
                else if (strTaskName == TaskName.Emergency)//急停
                {
                    ControlWordSet.ListValue.Add(6);
                }
                else if (strTaskName == TaskName.StopAction)//运动停止
                {
                    ControlWordSet.ListValue.Add(7);
                }
                else if (strTaskName == TaskName.FaultReset)//故障清除
                {
                    ControlWordSet.ListValue.Add(6);
                    ControlWordSet.ListValue.Add(128);
                    ControlWordSet.ListValue.Add(6);
                }
                else if (strTaskName == TaskName.AllFaultReset)//全部故障清除             由Lilbert与20220523添加全部故障清除
                {
                    ControlWordSet.ListValue.Add(6);
                    ControlWordSet.ListValue.Add(128);
                    ControlWordSet.ListValue.Add(6);

                    ControlWordSet.ListValue.Add(6);
                    ControlWordSet.ListValue.Add(128);
                    ControlWordSet.ListValue.Add(6);

                    ControlWordSet.ListValue.Add(6);
                    ControlWordSet.ListValue.Add(128);
                    ControlWordSet.ListValue.Add(6);

                    ControlWordSet.ListValue.Add(6);
                    ControlWordSet.ListValue.Add(128);
                    ControlWordSet.ListValue.Add(6);

                    ControlWordSet.ListValue.Add(6);
                    ControlWordSet.ListValue.Add(128);
                    ControlWordSet.ListValue.Add(6);

                    ControlWordSet.ListValue.Add(6);
                    ControlWordSet.ListValue.Add(128);
                    ControlWordSet.ListValue.Add(6);
                }
                else if (strTaskName == TaskName.StopPositionAction)//位置运动模式停止
                {
                    ControlWordSet.ListValue.Add(271);
                }
                else if (strTaskName == TaskName.AbsolutePositionAction)//绝对位置运动
                {
                    if (string.IsNullOrEmpty(SoftwareStateParameterSet.ServoStatus) || SoftwareStateParameterSet.ServoStatus == ServoStatus.ERROR)
                    {
                        ClearControlWordSet();
                        return RET.NO_EFFECT;//伺服状态异常
                    }
                    else if (SoftwareStateParameterSet.ServoStatus == ServoStatus.RUNING)
                    {
                        ControlWordSet.ListValue.Add(15);
                        ControlWordSet.ListValue.Add(63);
                    }
                    else if (SoftwareStateParameterSet.ServoStatus == ServoStatus.PREPARATION)
                    {
                        ControlWordSet.ListValue.Add(6);
                        ControlWordSet.ListValue.Add(7);
                        ControlWordSet.ListValue.Add(15);
                        ControlWordSet.ListValue.Add(63);
                    }
                }
                else if (strTaskName == TaskName.RelativePositionAction)//相对位置运动
                {
                    if (string.IsNullOrEmpty(SoftwareStateParameterSet.ServoStatus) || SoftwareStateParameterSet.ServoStatus == ServoStatus.ERROR)
                    {
                        ClearControlWordSet();
                        return RET.NO_EFFECT;//伺服状态异常
                    }
                    else if (SoftwareStateParameterSet.ServoStatus == ServoStatus.RUNING)
                    {
                        ControlWordSet.ListValue.Add(15);
                        ControlWordSet.ListValue.Add(127);
                    }
                    else if (SoftwareStateParameterSet.ServoStatus == ServoStatus.PREPARATION)
                    {
                        ControlWordSet.ListValue.Add(6);
                        ControlWordSet.ListValue.Add(7);
                        ControlWordSet.ListValue.Add(15);
                        ControlWordSet.ListValue.Add(127);
                    }
                }
                else if (strTaskName == TaskName.SpeedAction || strTaskName == TaskName.TorqueAction)//速度运动\转矩运动
                {
                    if (string.IsNullOrEmpty(SoftwareStateParameterSet.ServoStatus) || SoftwareStateParameterSet.ServoStatus == ServoStatus.ERROR)
                    {
                        ClearControlWordSet();
                        return RET.NO_EFFECT;//伺服状态异常
                    }
                    else if (SoftwareStateParameterSet.ServoStatus == ServoStatus.RUNING)
                    {
                        ControlWordSet.ListValue.Add(15);
                    }
                    else if (SoftwareStateParameterSet.ServoStatus == ServoStatus.PREPARATION)
                    {
                        ControlWordSet.ListValue.Add(6);
                        ControlWordSet.ListValue.Add(7);
                        ControlWordSet.ListValue.Add(15);
                    }
                }
                //else if (strTaskName == TaskName.PositionParameterTunning || strTaskName == TaskName.TorqueAction)//参数调优位置模式\参数调优速度模式    由Lilbert2022.11.22添加
                //{
                //    if (string.IsNullOrEmpty(SoftwareStateParameterSet.ServoStatus) || SoftwareStateParameterSet.ServoStatus == ServoStatus.ERROR)
                //    {
                //        ClearControlWordSet();
                //        return RET.NO_EFFECT;//伺服状态异常
                //    }
                //    //else if (SoftwareStateParameterSet.ServoStatus == ServoStatus.RUNING)
                //    //{
                //    //    ControlWordSet.ListValue.Add(15);
                //    //}
                //    else if (SoftwareStateParameterSet.ServoStatus == ServoStatus.PREPARATION)
                //    {
                //        OperatingControl("Fn Servo Off", "1", TaskName.FnDisabled, "");
                //        //ControlWordSet.ListValue.Add(6);
                //        //ControlWordSet.ListValue.Add(7);
                //        //ControlWordSet.ListValue.Add(15);
                //    }
                //}
                else if (strTaskName == TaskName.FunctionGenerator)//函数发生器
                {
                    if (string.IsNullOrEmpty(SoftwareStateParameterSet.ServoStatus) || SoftwareStateParameterSet.ServoStatus == ServoStatus.ERROR)
                    {
                        ClearControlWordSet();
                        return RET.NO_EFFECT;//伺服状态异常
                    }
                    else if (SoftwareStateParameterSet.ServoStatus == ServoStatus.RUNING)
                    {
                        ControlWordSet.ListValue.Add(15);
                        ControlWordSet.IsExistTaskAfterControlWord = true;
                    }
                    else if (SoftwareStateParameterSet.ServoStatus == ServoStatus.PREPARATION)
                    {
                        ControlWordSet.ListValue.Add(6);
                        ControlWordSet.ListValue.Add(7);
                        ControlWordSet.ListValue.Add(15);
                        ControlWordSet.IsExistTaskAfterControlWord = true;
                    }
                }
                else if (strTaskName == TaskName.SeekZero)//寻零模式
                {
                    if (string.IsNullOrEmpty(SoftwareStateParameterSet.ServoStatus) || SoftwareStateParameterSet.ServoStatus == ServoStatus.ERROR)
                    {
                        ClearControlWordSet();
                        return RET.NO_EFFECT;//伺服状态异常
                    }
                    else if (SoftwareStateParameterSet.ServoStatus == ServoStatus.RUNING)
                    {
                        ControlWordSet.ListValue.Add(31);
                    }
                    else if (SoftwareStateParameterSet.ServoStatus == ServoStatus.PREPARATION)
                    {
                        ControlWordSet.ListValue.Add(6);
                        ControlWordSet.ListValue.Add(7);
                        ControlWordSet.ListValue.Add(31);
                    }
                }
                else if (strTaskName == TaskName.StopSeekZero)//停止寻零模式
                {
                    ControlWordSet.ListValue.Add(15);
                }
                else
                {
                    ClearControlWordSet();
                }

                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OTHERSHELPER_WRITE_CONTROL_WORD_SET, "WriteControlWordSet", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：RefreshAllServoStatusHint
        //函数功能：更新所有伺服状态提示
        //
        //输入参数：string strTaskName   任务名称   
        //         
        //输出参数：NONE
        //        
        //编码作者：Ryan
        //更新时间：2020.10.19
        //*************************************************************************
        public static void RefreshAllServoStatusHint(string strTaskName)
        {
            if (ViewModelSet.Main == null)
            {
                return;
            }

            if (strTaskName == TaskName.ServoEnabled || strTaskName == TaskName.AbsolutePositionAction || strTaskName == TaskName.RelativePositionAction ||
                strTaskName == TaskName.SpeedAction || strTaskName == TaskName.TorqueAction || strTaskName == TaskName.OfflineInertiaIdentification)
            {
                SoftwareStateParameterSet.ServoStatus = ServoStatus.RUNING;
                ViewModelSet.Main.StatusWord = ServoStatus.RUNING;
                ViewModelSet.Main.StatusWordFontColor = BackgroundState.Green;
                ViewModelSet.Main.ServoStatusSwitchControl(false);
            }
            else if (strTaskName == TaskName.ServoDisabled)
            {
                SoftwareStateParameterSet.ServoStatus = ServoStatus.PREPARATION;
                ViewModelSet.Main.StatusWord = ServoStatus.PREPARATION;
                ViewModelSet.Main.StatusWordFontColor = BackgroundState.Blue;
                ViewModelSet.Main.ServoStatusSwitchControl(true);
            }
        }

        //*************************************************************************
        //函数名称：GetWindowsStartupPosition
        //函数功能：返回到初始位置
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.24
        //*************************************************************************
        public static bool GetWindowsStartupPosition()
        {
            if (WindowSet.clsMainWindow == null)
            {
                return false;
            }
            else
            {
                return WindowSet.clsMainWindow.GetWindowStartupLocation();
            }
        }

        //*************************************************************************
        //函数名称：MakeValueToDataTable
        //函数功能：生成DataTable数据表
        //
        //输入参数：string In_ReportName     报表内的名称
        //         string In_InterfaceName  接口名称
        //         string In_PropertyName   属性名称
        //         DataTable Out_dt         数据表
        //       
        //输出参数：NONE
        //        
        //编码作者：Ryan
        //更新时间：2019.11.13
        //*************************************************************************
        public static void MakeValueToDataTable(string In_RowName, string In_ReportName, string In_InterfaceName, string In_PropertyName, ref DataTable Out_dt)
        {
            try
            {
                if (Out_dt == null)
                {
                    Out_dt = new DataTable();
                }

                if (!Out_dt.Columns.Contains(In_RowName))
                {
                    Out_dt.Columns.Clear();
                    Out_dt.Columns.Add("ID", System.Type.GetType("System.String"));
                    Out_dt.Columns.Add("Index", System.Type.GetType("System.String"));
                    Out_dt.Columns.Add("Name", System.Type.GetType("System.String"));
                    Out_dt.Columns.Add(In_RowName, System.Type.GetType("System.String"));
                    Out_dt.Columns.Add("Default", System.Type.GetType("System.String"));
                }

                DataRow row = Out_dt.NewRow();
                row["ID"] = Convert.ToString(Out_dt.Rows.Count + 1);
                row["Name"] = In_ReportName;
                row[In_RowName] = In_InterfaceName;
                row["Default"] = In_PropertyName;
                row["Index"] = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", In_ReportName, "Index");

                Out_dt.Rows.Add(row);
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OTHERSHELPER_MAKE_VALUE_TO_DATATABLE, "MakeValueToDataTable", ex);
            }
        }

        public static void MakeValueToDataTable(string In_RowName, string In_ReportName, string In_InterfaceName, string In_PropertyName, string In_Unit, ref DataTable Out_dt)
        {
            try
            {
                if (Out_dt == null)
                {
                    Out_dt = new DataTable();
                }

                if (!Out_dt.Columns.Contains(In_RowName))
                {
                    Out_dt.Columns.Clear();
                    Out_dt.Columns.Add("ID", System.Type.GetType("System.String"));
                    Out_dt.Columns.Add("Index", System.Type.GetType("System.String"));
                    Out_dt.Columns.Add("Name", System.Type.GetType("System.String"));
                    Out_dt.Columns.Add(In_RowName, System.Type.GetType("System.String"));
                    Out_dt.Columns.Add("Default", System.Type.GetType("System.String"));
                    Out_dt.Columns.Add("Unit", System.Type.GetType("System.String"));
                }

                DataRow row = Out_dt.NewRow();
                row["ID"] = Convert.ToString(Out_dt.Rows.Count + 1);
                row["Name"] = In_ReportName;
                row[In_RowName] = In_InterfaceName;
                row["Default"] = In_PropertyName;
                row["Index"] = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", In_ReportName, "Index");
                row["Unit"] = In_Unit;
                Out_dt.Rows.Add(row);
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OTHERSHELPER_MAKE_VALUE_TO_DATATABLE, "MakeValueToDataTable", ex);
            }
        }

        public static void MakeValueToDataTable(string In_RowName, string In_InterfaceName, string In_Columns1Name, string In_Property1Name, string In_Columns2Name, string In_Property2Name, string In_Columns3Name, string In_Property3Name, string In_Columns4Name, string In_Property4Name, string In_Columns5Name, string In_Property5Name, string In_Columns6Name, string In_Property6Name, string In_Columns7Name, string In_Property7Name, string In_Columns8Name, string In_Property8Name, string In_Columns9Name, string In_Property9Name, ref DataTable Out_dt)
        {
            try
            {
                if (Out_dt == null)
                {
                    Out_dt = new DataTable();
                }

                if (!Out_dt.Columns.Contains(In_RowName))
                {
                    Out_dt.Columns.Clear();
                    Out_dt.Columns.Add("ID", System.Type.GetType("System.String"));
                    Out_dt.Columns.Add(In_Columns1Name, System.Type.GetType("System.String"));
                    Out_dt.Columns.Add(In_Columns2Name, System.Type.GetType("System.String"));
                    Out_dt.Columns.Add(In_Columns3Name, System.Type.GetType("System.String"));
                    Out_dt.Columns.Add(In_Columns4Name, System.Type.GetType("System.String"));
                    Out_dt.Columns.Add(In_Columns5Name, System.Type.GetType("System.String"));
                    Out_dt.Columns.Add(In_Columns6Name, System.Type.GetType("System.String"));
                    Out_dt.Columns.Add(In_Columns7Name, System.Type.GetType("System.String"));
                    Out_dt.Columns.Add(In_Columns8Name, System.Type.GetType("System.String"));
                    Out_dt.Columns.Add(In_Columns9Name, System.Type.GetType("System.String"));
                    Out_dt.Columns.Add(In_RowName, System.Type.GetType("System.String"));

                }

                DataRow row = Out_dt.NewRow();
                row["ID"] = Convert.ToString(Out_dt.Rows.Count + 1);
                row[In_Columns1Name] = In_Property1Name;
                row[In_Columns2Name] = In_Property2Name;
                row[In_Columns3Name] = In_Property3Name;
                row[In_Columns4Name] = In_Property4Name;
                row[In_Columns5Name] = In_Property5Name;
                row[In_Columns6Name] = In_Property6Name;
                row[In_Columns7Name] = In_Property7Name;
                row[In_Columns8Name] = In_Property8Name;
                row[In_Columns9Name] = In_Property9Name;

                row[In_RowName] = In_InterfaceName;

                Out_dt.Rows.Add(row);
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OTHERSHELPER_MAKE_VALUE_TO_DATATABLE, "MakeValueToDataTable", ex);
            }
        }

        //*************************************************************************
        //函数名称：RefreshAxisSet
        //函数功能：更新轴地址集合
        //
        //输入参数：NONE
        //       
        //输出参数：NONE
        //        
        //编码作者：Lilbert
        //更新时间：2023.02.06
        //*************************************************************************
        public static void RefreshAxisSet(int selectedConfigSlaveID, int selectedConfigAxisID, string slaveID)
        {
            SoftwareStateParameterSet.AxisIndex = 0;
            SoftwareStateParameterSet.lstAxisInfo = new List<AxisSet>();

            #region 2合一伺服
            if (selectedConfigSlaveID == 2 && selectedConfigAxisID == 2)
            {
                SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "00", AxisID = "00" });
                SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "00", AxisID = "01" });
            }
            #endregion
            #region 4合一伺服
            if (selectedConfigSlaveID == 2 && selectedConfigAxisID == 4)
            {
                if (slaveID == "SLAVE-1")
                {
                    SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "00", AxisID = "00" });
                    SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "00", AxisID = "01" });
                }
                else
                {
                    SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "01", AxisID = "00" });
                    SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "01", AxisID = "01" });
                }
            }
            #endregion
            #region 6合一伺服
            if (selectedConfigSlaveID == 3 && selectedConfigAxisID == 6)
            {
                if (slaveID == "SLAVE-1")
                {
                    SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "00", AxisID = "00" });
                    SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "00", AxisID = "01" });
                }
                else if (slaveID == "SLAVE-2")
                {
                    SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "01", AxisID = "00" });
                    SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "01", AxisID = "01" });
                }
                else
                {
                    SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "10", AxisID = "00" });
                    SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "10", AxisID = "01" });
                }
            }
            #endregion

            #region 单轴伺服
            if ((ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 1) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 2) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 3) ||
                (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 4) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 5) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 6) ||
                (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 7) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 8) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 9) ||
                (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 10) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 11) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 12) ||
                (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 13) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 14) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 15) ||
                (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 16) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 17) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 18) ||
                (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 19) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 20) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 21) ||
                (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 22) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 23) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 24) ||
                (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 25) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 26) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 27) ||
                (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 28) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 29) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 30) ||
                (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 31) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 32) )
            {
                if (slaveID == "SLAVE-1")
                {
                    SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "00", AxisID = "00" });
                    SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "00", AxisID = "00" });
                }
                else if (slaveID == "SLAVE-2")
                {
                    SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "01", AxisID = "00" });
                    SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "01", AxisID = "00" });
                }
                else if (slaveID == "SLAVE-3")
                {
                    SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "02", AxisID = "00" });
                    SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "02", AxisID = "00" });
                }
                else if (slaveID == "SLAVE-4")
                {
                    SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "03", AxisID = "00" });
                    SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "03", AxisID = "00" });
                }
                else if (slaveID == "SLAVE-5")
                {
                    SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "04", AxisID = "00" });
                    SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "04", AxisID = "00" });
                }
                else if (slaveID == "SLAVE-6")
                {
                    SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "05", AxisID = "00" });
                    SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "05", AxisID = "00" });
                }
                else if (slaveID == "SLAVE-7")
                {
                    SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "06", AxisID = "00" });
                    SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "06", AxisID = "00" });
                }
                else if (slaveID == "SLAVE-8")
                {
                    SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "07", AxisID = "00" });
                    SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "07", AxisID = "00" });
                }
                else if (slaveID == "SLAVE-9")
                {
                    SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "08", AxisID = "00" });
                    SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "08", AxisID = "00" });
                }
                else if (slaveID == "SLAVE-10")
                {
                    SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "09", AxisID = "00" });
                    SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "09", AxisID = "00" });
                }
                else if (slaveID == "SLAVE-11")
                {
                    SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "0A", AxisID = "00" });
                    SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "0A", AxisID = "00" });
                }
                else if (slaveID == "SLAVE-12")
                {
                    SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "0B", AxisID = "00" });
                    SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "0B", AxisID = "00" });
                }
                else if (slaveID == "SLAVE-13")
                {
                    SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "0C", AxisID = "00" });
                    SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "0C", AxisID = "00" });
                }
                else if (slaveID == "SLAVE-14")
                {
                    SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "0D", AxisID = "00" });
                    SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "0D", AxisID = "00" });
                }
                else if (slaveID == "SLAVE-15")
                {
                    SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "0E", AxisID = "00" });
                    SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "0E", AxisID = "00" });
                }
                else if (slaveID == "SLAVE-16")
                {
                    SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "0F", AxisID = "00" });
                    SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "0F", AxisID = "00" });
                }
                else if (slaveID == "SLAVE-17")
                {
                    SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "10", AxisID = "00" });
                    SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "10", AxisID = "00" });
                }
                else if (slaveID == "SLAVE-18")
                {
                    SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "11", AxisID = "00" });
                    SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "11", AxisID = "00" });
                }
                else if (slaveID == "SLAVE-19")
                {
                    SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "12", AxisID = "00" });
                    SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "12", AxisID = "00" });
                }
                else if (slaveID == "SLAVE-20")
                {
                    SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "13", AxisID = "00" });
                    SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "13", AxisID = "00" });
                }
                else if (slaveID == "SLAVE-21")
                {
                    SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "14", AxisID = "00" });
                    SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "14", AxisID = "00" });
                }
                else if (slaveID == "SLAVE-22")
                {
                    SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "15", AxisID = "00" });
                    SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "15", AxisID = "00" });
                }
                else if (slaveID == "SLAVE-23")
                {
                    SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "16", AxisID = "00" });
                    SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "16", AxisID = "00" });
                }
                else if (slaveID == "SLAVE-24")
                {
                    SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "17", AxisID = "00" });
                    SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "17", AxisID = "00" });
                }
                else if (slaveID == "SLAVE-25")
                {
                    SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "18", AxisID = "00" });
                    SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "18", AxisID = "00" });
                }
                else if (slaveID == "SLAVE-26")
                {
                    SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "19", AxisID = "00" });
                    SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "19", AxisID = "00" });
                }
                else if (slaveID == "SLAVE-27")
                {
                    SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "1A", AxisID = "00" });
                    SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "1A", AxisID = "00" });
                }
                else if (slaveID == "SLAVE-28")
                {
                    SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "1B", AxisID = "00" });
                    SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "1B", AxisID = "00" });
                }
                else if (slaveID == "SLAVE-29")
                {
                    SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "1C", AxisID = "00" });
                    SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "1C", AxisID = "00" });
                }
                else if (slaveID == "SLAVE-30")
                {
                    SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "1D", AxisID = "00" });
                    SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "1D", AxisID = "00" });
                }
                else if (slaveID == "SLAVE-31")
                {
                    SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "1E", AxisID = "00" });
                    SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "1E", AxisID = "00" });
                }
                else if (slaveID == "SLAVE-32")
                {
                    SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "1F", AxisID = "00" });
                    SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "1F", AxisID = "00" });
                }
                else
                {
                    SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "00", AxisID = "00" });
                    SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "00", AxisID = "00" });
                }
            }
            
            #endregion
            //else if (ARMIndex == "2")
            //{
            //    SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "01", AxisID = "00" });
            //    SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "01", AxisID = "01" });
            //}
            //else if (ARMIndex == "3")
            //{
            //    SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "10", AxisID = "00" });
            //    SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "10", AxisID = "01" });
            //}
            //else if (ARMIndex == "SystemReset")
            //{
            //    SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "00", AxisID = "00" });
            //    SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "01", AxisID = "00" });
            //    SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "10", AxisID = "00" });
            //}

        }

        //*************************************************************************
        //函数名称：RefreshAxisSet
        //函数功能：更新轴地址集合
        //
        //输入参数：NONE
        //       
        //输出参数：NONE
        //        
        //编码作者：Ryan
        //更新时间：2020.04.21
        //*************************************************************************
        public static void RefreshAxisSet(string ARMIndex)
        {
            SoftwareStateParameterSet.AxisIndex = 0;
            SoftwareStateParameterSet.lstAxisInfo = new List<AxisSet>();

            if (ARMIndex == "1")
            {
                SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "00", AxisID = "00" });
                SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "00", AxisID = "01" });
            }
            else if (ARMIndex == "2")
            {
                SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "01", AxisID = "00" });
                SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "01", AxisID = "01" });
            }
            else if (ARMIndex == "3")
            {
                SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "10", AxisID = "00" });
                SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "10", AxisID = "01" });
            }
            else if (ARMIndex == "SystemReset")
            {
                SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "00", AxisID = "00" });
                SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "01", AxisID = "00" });
                SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "10", AxisID = "00" });
            }
            else
            {
                SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "00", AxisID = "00" });
                SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "00", AxisID = "01" });

                SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "01", AxisID = "00" });
                SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "01", AxisID = "01" });

                SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "10", AxisID = "00" });
                SoftwareStateParameterSet.lstAxisInfo.Add(new AxisSet() { SlaveID = "10", AxisID = "01" });
            }
        }

        //*************************************************************************
        //函数名称：ClearFirmwareUpdateSet
        //函数功能：清空固件升级信息集合
        //
        //输入参数：NONE
        //       
        //输出参数：NONE
        //        
        //编码作者：Ryan
        //更新时间：2020.04.29
        //*************************************************************************
        public static void ClearFirmwareUpdateSet()
        {
            FirmwareUpdateSet.IsUpdate = false;
            FirmwareUpdateSet.Process = FirmwareUpdateProcess.NONE;

            FirmwareUpdateSet.Offset = 0;
            FirmwareUpdateSet.PackageNumber = 0;
            FirmwareUpdateSet.DataSize = 0;

            FirmwareUpdateSet.ARMIndex = 0;

            FirmwareUpdateSet.ReceivingTimes = 0;
        }
        public static void ClearFirmwareUpdateSet_ExceptARMIndex()
        {
            FirmwareUpdateSet.IsUpdate = false;
            FirmwareUpdateSet.Process = FirmwareUpdateProcess.NONE;

            FirmwareUpdateSet.Offset = 0;
            FirmwareUpdateSet.PackageNumber = 0;
            FirmwareUpdateSet.DataSize = 0;

            FirmwareUpdateSet.ReceivingTimes = 0;
        }

        //*************************************************************************
        //函数名称：FirmwareUpdateSetInitialize
        //函数功能：初始化固件升级信息集合
        //
        //输入参数：NONE
        //       
        //输出参数：NONE
        //        
        //编码作者：Ryan
        //更新时间：2020.04.29
        //*************************************************************************
        public static void FirmwareUpdateSetInitialize()
        {
            FirmwareUpdateSet.IsUpdate = false;
            FirmwareUpdateSet.Process = FirmwareUpdateProcess.NONE;

            FirmwareUpdateSet.FileName = null;
            FirmwareUpdateSet.FileSize = 0;
            FirmwareUpdateSet.FileContent = null;

            FirmwareUpdateSet.Offset = 0;
            FirmwareUpdateSet.PackageNumber = 0;
            FirmwareUpdateSet.DataSize = 0;

            FirmwareUpdateSet.ProcessNotification = new StringBuilder();

            FirmwareUpdateSet.ARM = new List<int>();
            FirmwareUpdateSet.ARMIndex = 0;

            FirmwareUpdateSet.ReceivingTimes = 0;
        }

        //*************************************************************************
        //函数名称：GetRMSValue
        //函数功能：获取均方根
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.06.02
        //*************************************************************************
        public static int GetRMSValue(List<int> lstValue, ref double dRMS)
        {
            int iLength = 0;
            UInt64 uResult = 0;

            try
            {
                if (lstValue == null)
                {
                    return RET.ERROR;
                }

                if (lstValue.Count == 0)
                {
                    return RET.NO_EFFECT;
                }
                else
                {
                    iLength = lstValue.Count;
                }

                foreach (int item in lstValue)
                {
                    uResult += (UInt64)item * (UInt64)item;
                }

                dRMS = Math.Round(Math.Sqrt((double)uResult / iLength), 2);
                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OTHERSHELPER_GET_RMS_VALUE, "GetRMSValue", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：GetAlarmSet
        //函数功能：获取故障集合
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.06.05
        //*************************************************************************
        public static string GetAlarmSet(string strValue)
        {
            UInt32 uValue = 0;
            string strValue32_Zero = null;
            string strValue32 = null;
            string strAddBit = null;

            try
            {
                for (int i = 0; i < 32; i++)
                {
                    strValue32_Zero += "0";
                }

                //如果是空，回复32位0
                if (string.IsNullOrEmpty(strValue))
                {
                    return strValue32_Zero;
                }

                //转换为2进制
                uValue = Convert.ToUInt32(strValue);
                strValue32 = Convert.ToString(uValue, 2);

                //如果是空，回复32位0
                if (string.IsNullOrEmpty(strValue32))
                {
                    return strValue32_Zero;
                }

                //补位
                for (int i = strValue32.Length; i < 32; i++)
                {
                    strAddBit += "0";
                }

                return (strAddBit + strValue32);
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OTHERSHELPER_GET_ALARM_SET, "GetAlarmSet", ex);
                return strValue32_Zero;
            }
        }

        //*************************************************************************
        //函数名称：GetInitialConfigInfo
        //函数功能：获取配置文件
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan&Lilbert
        //更新时间：2020.06.10&2022.08.31
        //*************************************************************************
        public static int GetInitialConfigInfo()
        {
            string bSwitch = null;
            string ParameterPath = null;

            try
            {
                if (File.Exists(FilePath.Ini))
                {
                    ParameterPath = IniHelper.IniReadValue("ServoStudio", "Path", FilePath.Ini);
                    FilePath.Parameter = string.Concat(FilePath.MotorAndServoParameter, ParameterPath);//由Lilbert增加选择伺服驱动器名称时，加载相应驱动器和电机参数
                    SoftwareStateParameterSet.ImportConfigurationAddress = FilePath.Parameter;

                    bSwitch = IniHelper.IniReadValue("ServoStudio", "AlarmAutoExpand", FilePath.Ini);
                    if (bSwitch == "true")
                    {
                        SoftwareStateParameterSet.IsAlarmAutoExpand = true;
                    }
                    else
                    {
                        SoftwareStateParameterSet.IsAlarmAutoExpand = false;
                    }

                    bSwitch = IniHelper.IniReadValue("ServoStudio", "OneKeyShortcut", FilePath.Ini);
                    if (bSwitch == "true")
                    {
                        SoftwareStateParameterSet.IsOneKeyShortCut = true;
                    }
                    else
                    {
                        SoftwareStateParameterSet.IsOneKeyShortCut = false;
                    }

                    return RET.SUCCEEDED;
                }
                else
                {
                    return RET.ERROR;
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OTHERSHELPER_GET_INITIAL_CONFIG_INFO, "GetInitialConfigInfo", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：GetCompanyInfoConfigInfo
        //函数功能：获取公司信息配置文件
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.10.07
        //*************************************************************************
        public static int GetCompanyInfoConfigInfo()
        {
            string bSwitch = null;
            string ParameterDepartment = null;

            try
            {
                if (File.Exists(CompanyPath.CompanyInformation))
                {
                    GlobalCompanyInfo.CompanyDepartment = IniHelper.IniReadValue("Company", "CompanyDepartment", CompanyPath.CompanyInformation);
                    GlobalCompanyInfo.CompanyDepartment1 = IniHelper.IniReadValue("Company", "CompanyDepartment1", CompanyPath.CompanyInformation);
                    GlobalCompanyInfo.CompanyWeb = IniHelper.IniReadValue("Company", "CompanyWeb", CompanyPath.CompanyInformation);
                    GlobalCompanyInfo.CompanyName = IniHelper.IniReadValue("Company", "CompanyName", CompanyPath.CompanyInformation);
                    //FilePath.Parameter = string.Concat(FilePath.MotorAndServoParameter, ParameterPath);//由Lilbert增加选择伺服驱动器名称时，加载相应驱动器和电机参数
                    //SoftwareStateParameterSet.ImportConfigurationAddress = FilePath.Parameter;

                    //bSwitch = IniHelper.IniReadValue("ServoStudio", "AlarmAutoExpand", FilePath.Ini);
                    //if (bSwitch == "true")
                    //{
                    //    SoftwareStateParameterSet.IsAlarmAutoExpand = true;
                    //}
                    //else
                    //{
                    //    SoftwareStateParameterSet.IsAlarmAutoExpand = false;
                    //}

                    //bSwitch = IniHelper.IniReadValue("ServoStudio", "OneKeyShortcut", FilePath.Ini);
                    //if (bSwitch == "true")
                    //{
                    //    SoftwareStateParameterSet.IsOneKeyShortCut = true;
                    //}
                    //else
                    //{
                    //    SoftwareStateParameterSet.IsOneKeyShortCut = false;
                    //}

                    return RET.SUCCEEDED;
                }
                else
                {
                    return RET.ERROR;
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OTHERSHELPER_GET_INITIAL_CONFIG_INFO, "GetCompanyInfoConfigInfo", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：Synchronization
        //函数功能：系统对时
        //
        //输入参数：NONE
        //         
        //输出参数：1：SUCCEED    
        //        -1: ERROR
        //        
        //编码作者：Ryan&Lilbert
        //更新时间：2020.06.09&2021.11.15
        //*************************************************************************
        public static void AssignSynchronizationTask()
        {
            UInt32 uTimeSpan = 0;
            Dictionary<string, string> dicParameterInfo = new Dictionary<string, string>();
            List<ParameterReadWriteSet> lstParameterInfo = new List<ParameterReadWriteSet>();
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            try
            {
                DateTime dtCurrent = DateTime.Now;
                DateTime dt1970 = new DateTime(1970, 1, 1);

                TimeSpan span = dtCurrent.Subtract(dt1970);
                uTimeSpan = (UInt32)span.TotalSeconds;

                dicParameterInfo.Add("Timestamp", uTimeSpan.ToString());

                OthersHelper.GetParameterForWrite(dicParameterInfo, ref lstParameterInfo);

                ParameterReadWriteModel.GetIndexAndDataType(lstParameterInfo, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true, IsEEPROM: false);
                //ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterWrite(PageName.MAIN, TaskName.Timestamp, lstTransmittingDataInfo);
                //ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterWrite_For_Timestamp(PageName.MAIN, TaskName.Timestamp, lstTransmittingDataInfo);     //由Lilbert添加，为3个芯片写时间戳
                if (Common.CurrentLanguageFile == @"Language\zh-CN.xaml")
                {
                    ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterWrite_For_Timestamp(PageName.MAIN, TaskName.Timestamp, lstTransmittingDataInfo);     //由Lilbert添加，为3个芯片写时间戳
                }
                else
                {
                    ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterWrite_For_Timestamp(PageName.MAIN, TaskName.Timestamp1, lstTransmittingDataInfo);     //由Lilbert添加，为3个芯片写时间戳
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OTHERSHELPER_SYNCHRONIZATION, "Synchronization", ex);
            }
        }

        //*************************************************************************
        //函数名称：AssignUnitExchangeTask
        //函数功能：下达单位换算任务
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan&Lilbert
        //更新时间：2020.06.10&2023.02.08
        //*************************************************************************
        public static void AssignUnitExchangeTask()
        {
            int iRet = -1;
            Dictionary<string, string> dicParameterInfo = new Dictionary<string, string>();
            ObservableCollection<ParameterReadWriteSet> obsParameterInfo = new ObservableCollection<ParameterReadWriteSet>();
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            iRet = HexHelper.CheckSerialPortStatus();
            if (iRet != RET.SUCCEEDED)
            {
                return;
            }

            dicParameterInfo.Add("Motor Rated Torque", null);//200006
            dicParameterInfo.Add("Encoder Type", null);//200012
            dicParameterInfo.Add("Abs Encoder Single-Turn Bit", null);//200014
            dicParameterInfo.Add("Abz Encoder Pulses", null);//200016  
            dicParameterInfo.Add("Linear Motor Pitch", null);//200027  //由Lilbert于2023.02.08添加读取直线电机节距
            dicParameterInfo.Add("Motor Type", null);//200001    //由Lilbert于2023.02.08添加读取电机类型            
            OthersHelper.GetParameterForRead(dicParameterInfo, ref obsParameterInfo);

            ParameterReadWriteModel.GetIndexAndDataType(obsParameterInfo, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true);
            ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterRead(PageName.MAIN, TaskName.UnitExchanged, lstTransmittingDataInfo);
        }

        //*************************************************************************
        //函数名称：GetUnitExchangedInfo
        //函数功能：获取单位转换信息
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan&Lilbert
        //更新时间：2020.06.10&Lilbert
        //*************************************************************************
        public static int GetUnitExchangedInfo()
        {
            const double doublePi = 3.1415926 * 2;
            double dAbsolute = 0;
            double dIncremental = 0;
            double dTorque = 0;

            double dMotorPitch = 0;
            double dMotorLines = 0;
            double dMotorEncoderPulses = 0;

            string strEncoderType;
            string strAbsolute;
            string strIncremental;
            string strTorque;

            string strMotorPitch;
            string strMotorLines;
            string strMotorEncoderPulses;

            try
            {
                //获取单位转换参数
                strEncoderType = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Encoder Type", "Index"));//编码器类型
                strAbsolute = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Abs Encoder Single-Turn Bit", "Index"));//绝对式 200014
                strIncremental = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Abz Encoder Pulses", "Index"));//增量式 200016
                strTorque = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Motor Rated Torque", "Index"));//额定转矩 200006

                strMotorPitch = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Linear Motor Pitch", "Index"));//电机节距 200027    //由Lilbert增加电机节距
                strMotorLines = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Pitch Motor Encoder Lines", "Index"));//直线电机编码器线数 20001B    //由Lilbert增加直线电机编码器线数
                strMotorEncoderPulses = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Abz Encoder Pulses", "Index"));//    //由Lilbert增加

                //如有参数为空，则为默认的单位
                if (string.IsNullOrEmpty(strEncoderType) || string.IsNullOrEmpty(strAbsolute) || string.IsNullOrEmpty(strIncremental) || string.IsNullOrEmpty(strTorque))
                {
                    GetDefaultUnit();
                    GetSelectDefaultUnit();

                    GetOscilloscopeParameterUnitSet();
                    return RET.ERROR;
                }
                else
                {
                    dAbsolute = Convert.ToDouble(strAbsolute);
                    dIncremental = Convert.ToDouble(strIncremental);
                    dTorque = Convert.ToDouble(strTorque);
                    dMotorPitch = Convert.ToDouble(strMotorPitch);
                    dMotorLines = Convert.ToDouble(strMotorLines);
                    dMotorEncoderPulses = Convert.ToDouble(strMotorEncoderPulses);
                }

                //判断编码器类型
                switch (strEncoderType)
                {
                    case "1":
                    case "8":                                           //由Lilbert添加BISS-C增量式编码器
                    case "9":  //由Lilbert添加ABZ增量式编码器+HALL
                        CurrentUnit.EncodeType = "Incremental";
                        GetSelectedUnit(CurrentUnit.bInitialized);
                        GetOscilloscopeParameterUnitSet();
                        break;
                    case "2":
                    case "3":
                    case "4":
                    case "5":                                          //由Lilbert添加旋转式编码器
                    case "7":
                    case "10":                                         //由Lilbert添加HALL-UVW
                    case "11":                                         //由Lilbert添加SSI编码器
                        CurrentUnit.EncodeType = "Absolute";
                        GetSelectedUnit(CurrentUnit.bInitialized);
                        GetOscilloscopeParameterUnitSet();
                        break;
                    case "6":
                        CurrentUnit.EncodeType = "CommunicationIncremental";    //由Lilbert添加通信型增量式编码器
                        GetSelectedUnit(CurrentUnit.bInitialized);
                        GetOscilloscopeParameterUnitSet();
                        break;
                    default:
                        GetDefaultUnit();
                        GetSelectDefaultUnit();

                        GetOscilloscopeParameterUnitSet();
                        return RET.NO_EFFECT;
                }

                CurrentUnit.ListAbsolute.Clear();
                CurrentUnit.ListIncremental.Clear();

                //默认单位
                CurrentUnit.ListIncremental.Add(new UnitExchangedSet() { Content = "cnt-cnt", Value = 1 });
                CurrentUnit.ListIncremental.Add(new UnitExchangedSet() { Content = "cnt/s-cnt/s", Value = 1 });
                CurrentUnit.ListIncremental.Add(new UnitExchangedSet() { Content = "cnt/s^2-cnt/s^2", Value = 1 });
                CurrentUnit.ListIncremental.Add(new UnitExchangedSet() { Content = "0.1%额定扭矩-0.1%额定扭矩", Value = 1 });

                CurrentUnit.ListAbsolute.Add(new UnitExchangedSet() { Content = "cnt-cnt", Value = 1 });
                CurrentUnit.ListAbsolute.Add(new UnitExchangedSet() { Content = "cnt/s-cnt/s", Value = 1 });
                CurrentUnit.ListAbsolute.Add(new UnitExchangedSet() { Content = "cnt/s^2-cnt/s^2", Value = 1 });
                CurrentUnit.ListAbsolute.Add(new UnitExchangedSet() { Content = "0.1%额定扭矩-0.1%额定扭矩", Value = 1 });

                // 位置单位
                CurrentUnit.ListIncremental.Add(new UnitExchangedSet() { Content = "cnt-revs", Value = Math.Round(1 / dIncremental, 10) });
                CurrentUnit.ListIncremental.Add(new UnitExchangedSet() { Content = "revs-cnt", Value = dIncremental });
                CurrentUnit.ListIncremental.Add(new UnitExchangedSet() { Content = "cnt-deg", Value = Math.Round(doublePi / dIncremental, 10) });
                CurrentUnit.ListIncremental.Add(new UnitExchangedSet() { Content = "deg-cnt", Value = Math.Round(dIncremental / doublePi, 10) });

                CurrentUnit.ListIncremental.Add(new UnitExchangedSet() { Content = "cnt-mm", Value = Math.Round(dMotorPitch / dMotorEncoderPulses, 10) });  //由Lilbert增加cnt-mm位置单位转换
                CurrentUnit.ListIncremental.Add(new UnitExchangedSet() { Content = "mm-cnt", Value = Math.Round(dMotorEncoderPulses / dMotorPitch, 10) });  //由Lilbert增加mm-cnt位置单位转换

                CurrentUnit.ListAbsolute.Add(new UnitExchangedSet() { Content = "cnt-revs", Value = Math.Round(1 / (Math.Pow(2, dAbsolute)), 10) });
                CurrentUnit.ListAbsolute.Add(new UnitExchangedSet() { Content = "revs-cnt", Value = Math.Pow(2, dAbsolute) });
                CurrentUnit.ListAbsolute.Add(new UnitExchangedSet() { Content = "cnt-deg", Value = Math.Round(doublePi / (Math.Pow(2, dAbsolute)), 10) });
                CurrentUnit.ListAbsolute.Add(new UnitExchangedSet() { Content = "deg-cnt", Value = Math.Round((Math.Pow(2, dAbsolute)) / doublePi, 10) });

                CurrentUnit.ListAbsolute.Add(new UnitExchangedSet() { Content = "cnt-mm", Value = Math.Round(dMotorPitch / dMotorEncoderPulses, 10) });  //由Lilbert增加cnt-mm位置单位转换
                CurrentUnit.ListAbsolute.Add(new UnitExchangedSet() { Content = "mm-cnt", Value = Math.Round(dMotorEncoderPulses / dMotorPitch, 10) });  //由Lilbert增加mm-cnt位置单位转换

                //速度单位
                CurrentUnit.ListIncremental.Add(new UnitExchangedSet() { Content = "cnt/s-rpm", Value = Math.Round(60 / dIncremental, 10) });
                CurrentUnit.ListIncremental.Add(new UnitExchangedSet() { Content = "rpm-cnt/s", Value = Math.Round(dIncremental / 60, 10) });
                CurrentUnit.ListIncremental.Add(new UnitExchangedSet() { Content = "cnt/s-rps", Value = Math.Round(1 / dIncremental, 10) });
                CurrentUnit.ListIncremental.Add(new UnitExchangedSet() { Content = "rps-cnt/s", Value = dIncremental });
                CurrentUnit.ListIncremental.Add(new UnitExchangedSet() { Content = "cnt/s-deg/s", Value = Math.Round(doublePi / dIncremental, 10) });
                CurrentUnit.ListIncremental.Add(new UnitExchangedSet() { Content = "deg/s-cnt/s", Value = Math.Round(dIncremental / doublePi, 10) });

                CurrentUnit.ListIncremental.Add(new UnitExchangedSet() { Content = "cnt/s-mm/s", Value = Math.Round(dMotorPitch / dMotorEncoderPulses, 10) });   //由Lilbert增加cnt/s-mm/s位置单位转换
                CurrentUnit.ListIncremental.Add(new UnitExchangedSet() { Content = "mm/s-cnt/s", Value = Math.Round(dMotorEncoderPulses / dMotorPitch, 10) });   //由Lilbert增加mm/s-cnt/s位置单位转换

                CurrentUnit.ListAbsolute.Add(new UnitExchangedSet() { Content = "cnt/s-rpm", Value = Math.Round(60 / (Math.Pow(2, dAbsolute)), 10) });
                CurrentUnit.ListAbsolute.Add(new UnitExchangedSet() { Content = "rpm-cnt/s", Value = Math.Round((Math.Pow(2, dAbsolute)) / 60, 10) });
                CurrentUnit.ListAbsolute.Add(new UnitExchangedSet() { Content = "cnt/s-rps", Value = Math.Round(1 / (Math.Pow(2, dAbsolute)), 10) });
                CurrentUnit.ListAbsolute.Add(new UnitExchangedSet() { Content = "rps-cnt/s", Value = Math.Pow(2, dAbsolute) });
                CurrentUnit.ListAbsolute.Add(new UnitExchangedSet() { Content = "cnt/s-deg/s", Value = Math.Round(doublePi / (Math.Pow(2, dAbsolute)), 10) });
                CurrentUnit.ListAbsolute.Add(new UnitExchangedSet() { Content = "deg/s-cnt/s", Value = Math.Round((Math.Pow(2, dAbsolute)) / doublePi, 10) });

                CurrentUnit.ListAbsolute.Add(new UnitExchangedSet() { Content = "cnt/s-mm/s", Value = Math.Round(dMotorPitch / dMotorEncoderPulses, 10) });   //由Lilbert增加cnt/s-mm/s位置单位转换
                CurrentUnit.ListAbsolute.Add(new UnitExchangedSet() { Content = "mm/s-cnt/s", Value = Math.Round(dMotorEncoderPulses / dMotorPitch, 10) });   //由Lilbert增加mm/s-cnt/s位置单位转换

                //加速度单位
                CurrentUnit.ListIncremental.Add(new UnitExchangedSet() { Content = "cnt/s^2-rpm/s", Value = Math.Round(60 / dIncremental, 10) });
                CurrentUnit.ListIncremental.Add(new UnitExchangedSet() { Content = "rpm/s-cnt/s^2", Value = Math.Round(dIncremental / 60, 10) });
                CurrentUnit.ListIncremental.Add(new UnitExchangedSet() { Content = "cnt/s^2-rps/s", Value = Math.Round(1 / dIncremental, 10) });
                CurrentUnit.ListIncremental.Add(new UnitExchangedSet() { Content = "rps/s-cnt/s^2", Value = dIncremental });
                CurrentUnit.ListIncremental.Add(new UnitExchangedSet() { Content = "cnt/s^2-deg/s^2", Value = Math.Round(doublePi / dIncremental, 10) });
                CurrentUnit.ListIncremental.Add(new UnitExchangedSet() { Content = "deg/s^2-cnt/s^2", Value = Math.Round(dIncremental / doublePi, 10) });

                CurrentUnit.ListIncremental.Add(new UnitExchangedSet() { Content = "cnt/s^2-mm/s^2", Value = Math.Round(dMotorPitch / dMotorEncoderPulses, 10) });   //由Lilbert增加cnt/s^2-mm/s^2位置单位转换
                CurrentUnit.ListIncremental.Add(new UnitExchangedSet() { Content = "mm/s^2-cnt/s^2", Value = Math.Round(dMotorEncoderPulses / dMotorPitch, 10) });   //由Lilbert增加mm/s^2-cnt/s^2位置单位转换

                CurrentUnit.ListAbsolute.Add(new UnitExchangedSet() { Content = "cnt/s^2-rpm/s", Value = Math.Round(60 / (Math.Pow(2, dAbsolute)), 10) });
                CurrentUnit.ListAbsolute.Add(new UnitExchangedSet() { Content = "rpm/s-cnt/s^2", Value = Math.Round((Math.Pow(2, dAbsolute)) / 60, 10) });
                CurrentUnit.ListAbsolute.Add(new UnitExchangedSet() { Content = "cnt/s^2-rps/s", Value = Math.Round(1 / (Math.Pow(2, dAbsolute)), 10) });
                CurrentUnit.ListAbsolute.Add(new UnitExchangedSet() { Content = "rps/s-cnt/s^2", Value = Math.Pow(2, dAbsolute) });
                CurrentUnit.ListAbsolute.Add(new UnitExchangedSet() { Content = "cnt/s^2-deg/s^2", Value = Math.Round(doublePi / (Math.Pow(2, dAbsolute)), 10) });
                CurrentUnit.ListAbsolute.Add(new UnitExchangedSet() { Content = "deg/s^2-cnt/s^2", Value = Math.Round((Math.Pow(2, dAbsolute)) / doublePi, 10) });

                CurrentUnit.ListAbsolute.Add(new UnitExchangedSet() { Content = "cnt/s^2-mm/s^2", Value = Math.Round(dMotorPitch / dMotorEncoderPulses, 10) });   //由Lilbert增加cnt/s^2-mm/s^2位置单位转换
                CurrentUnit.ListAbsolute.Add(new UnitExchangedSet() { Content = "mm/s^2-cnt/s^2", Value = Math.Round(dMotorEncoderPulses / dMotorPitch, 10) });   //由Lilbert增加mm/s^2-cnt/s^2位置单位转换

                //转矩
                CurrentUnit.ListIncremental.Add(new UnitExchangedSet() { Content = "0.1%额定扭矩-Nm", Value = Math.Round(0.00001 * dTorque, 10) });
                CurrentUnit.ListIncremental.Add(new UnitExchangedSet() { Content = "Nm-0.1%额定扭矩", Value = Math.Round(100000 / dTorque, 10) });

                CurrentUnit.ListAbsolute.Add(new UnitExchangedSet() { Content = "0.1%额定扭矩-Nm", Value = Math.Round(0.00001 * dTorque, 10) });
                CurrentUnit.ListAbsolute.Add(new UnitExchangedSet() { Content = "Nm-0.1%额定扭矩", Value = Math.Round(100000 / dTorque, 10) });

                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                GetDefaultUnit();
                GetSelectDefaultUnit();

                GetOscilloscopeParameterUnitSet();

                SoftwareErrorHelper.CatchDispose(ERROR.OTHERSHELPER_GET_UNIT_EXCHANGED_INFO, "GetUnitExchangedInfo", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：ExchangeUnit
        //函数功能：单位换算
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.06.11
        //*************************************************************************
        public static string ExchangeUnit(string ItemName, string strContent, string strValue)
        {
            int iIndex = -1;
            int iLength = -1;
            string strTemp = null;
            List<UnitExchangedSet> lstUnit = new List<UnitExchangedSet>();

            try
            {
                //输入内容校验
                if (string.IsNullOrEmpty(strValue) || string.IsNullOrEmpty(strContent))
                {
                    return null;
                }

                //判断输入数据是否为16进制数
                if (OthersHelper.CheckIsInputHex(strValue))
                {
                    var query = GlobalParameterSet.dt.AsEnumerable().Where(o => o.Field<string>("Name") == ItemName).FirstOrDefault();
                    if (query == null)
                    {
                        return Convert.ToString(RET.NO_EFFECT);
                    }
                    else
                    {
                        string strDataType = query.Field<string>("DataType");
                        strValue = OthersHelper.TransferHexToDecimal(strValue, strDataType);
                    }
                }

                //是否输入的是数字-可能有小数
                if (!IsInputNumber(strValue))
                {
                    return null;
                }

                //选择单位公式
                if (CurrentUnit.EncodeType == "Absolute")
                {
                    CurrentUnit.ListAbsolute.ForEach(item => lstUnit.Add(item));
                }
                else
                {
                    CurrentUnit.ListIncremental.ForEach(item => lstUnit.Add(item));
                }

                //单位转化
                UnitExchangedSet unitExchangedSet = lstUnit.Where(o => o.Content == strContent).FirstOrDefault<UnitExchangedSet>();
                if (unitExchangedSet != null)
                {
                    iLength = unitExchangedSet.Content.Length;
                    iIndex = unitExchangedSet.Content.IndexOf("-");
                    strTemp = unitExchangedSet.Content.Substring(iIndex + 1, iLength - iIndex - 1);

                    if (strTemp == DefaultUnit.PositionUnit || strTemp == DefaultUnit.TorqueUnit || strTemp == DefaultUnit.SpeedUnit || strTemp == DefaultUnit.AccelerationUnit)
                    {
                        double dValue = DecimalExchange(strValue);
                        Int64 iValue = Convert.ToInt64(dValue * unitExchangedSet.Value);

                        return Convert.ToString(iValue);
                    }
                    else
                    {
                        Int64 iValue = Convert.ToInt64(strValue);
                        double dValue = Math.Round(Convert.ToDouble(iValue * unitExchangedSet.Value), 2);

                        return Convert.ToString(dValue);
                    }
                }
                else
                {
                    return null;
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OTHERSHELPER_EXCHANGE_UNIT, "ExchangeUnit", ex);
                return null;
            }
        }

        //*************************************************************************
        //函数名称：DecimalExchange
        //函数功能：小数转换
        //
        //输入参数：string strValue  输入数据
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.11.25
        //*************************************************************************
        public static double DecimalExchange(string strValue)
        {
            double dRet = 0;

            try
            {
                //如果是空
                if (string.IsNullOrEmpty(strValue))
                {
                    return 0;
                }

                //如果不是数字
                if (!IsInputNumber(strValue))
                {
                    return 0;
                }

                //如果是整数
                if (IsInputInteger(strValue))
                {
                    dRet = Convert.ToDouble(strValue);
                }
                else//如果是小数
                {
                    int iPoint = strValue.IndexOf(".");
                    int iLength = strValue.Length;
                    int iDelete = iLength - iPoint - 2;

                    if (iLength - iPoint - 1 == 1)
                    {
                        dRet = Convert.ToDouble(strValue);
                    }
                    else
                    {
                        dRet = Convert.ToDouble(strValue.Substring(0, iLength - iDelete));
                    }
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OTHERSHELPER_DECIMAL_EXCHANGE, " DecimalExchange", ex);
            }

            return dRet;
        }

        //*************************************************************************
        //函数名称：CheckInputParametersCorrected_For_MotorFeedback
        //函数功能：判断输入参数是否正确
        //
        //输入参数：List<ParameterReadWriteSet> lstParameterInfo     参数集合
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2022.11.24
        //*************************************************************************
        public static int CheckInputParametersCorrected_For_MotorFeedback(ref List<ParameterReadWriteSet> lstParameterInfo)
        {
            bool bRet = false;
            string strCurrentValue = null;

            if (lstParameterInfo == null)
            {
                return RET.ERROR;
            }

            foreach (var item in lstParameterInfo)
            {
                //判断是否为空
                if (string.IsNullOrEmpty(item.CurrentValue))
                {
                    return RET.NO_EFFECT;
                }

                //判断输入数据是否为16进制数
                if (OthersHelper.CheckIsInputHex(item.CurrentValue))
                {
                    item.CurrentValue = OthersHelper.TransferHexToDecimal(item.CurrentValue, item.DataType);
                }

                //判断输入数据是否为整数
                if (!IsInputInteger(item.CurrentValue))
                {
                    return RET.NO_EFFECT;
                }

                //判断输入数据是否在数据类型范围内
                strCurrentValue = item.CurrentValue;
                bRet = IsOutOfRange(ref strCurrentValue, item.Unit, item.DataType, item.Max, item.Min);
                if (bRet)
                {
                    item.CurrentValue = strCurrentValue;
                }
                else
                {
                    ViewModelSet.Main?.ShowNotification_For_MotorFeedback(item.Name, item.Description, item.Min, item.Max, item.Unit, bExchanged: true);
                    return RET.ERROR;
                }
            }

            return RET.SUCCEEDED;
        }

        //*************************************************************************
        //函数名称：CheckInputParametersCorrected
        //函数功能：判断输入参数是否正确
        //
        //输入参数：List<ParameterReadWriteSet> lstParameterInfo     参数集合
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.06.15
        //*************************************************************************
        public static int CheckInputParametersCorrected(ref List<ParameterReadWriteSet> lstParameterInfo)
        {
            bool bRet = false;
            string strCurrentValue = null;

            if (lstParameterInfo == null)
            {
                return RET.ERROR;
            }

            foreach (var item in lstParameterInfo)
            {
                //判断是否为空
                if (string.IsNullOrEmpty(item.CurrentValue))
                {
                    return RET.NO_EFFECT;
                }

                //判断输入数据是否为16进制数
                if (OthersHelper.CheckIsInputHex(item.CurrentValue))
                {
                    item.CurrentValue = OthersHelper.TransferHexToDecimal(item.CurrentValue, item.DataType);
                }

                //判断输入数据是否为整数
                if (!IsInputInteger(item.CurrentValue))
                {
                    return RET.NO_EFFECT;
                }

                //判断输入数据是否在数据类型范围内
                strCurrentValue = item.CurrentValue;
                bRet = IsOutOfRange(ref strCurrentValue, item.Unit, item.DataType, item.Max, item.Min);
                if (bRet)
                {
                    item.CurrentValue = strCurrentValue;
                }
                else
                {
                    ViewModelSet.Main?.ShowNotification(item.Name, item.Description, item.Min, item.Max, item.Unit, bExchanged: true);
                    return RET.ERROR;
                }
            }

            return RET.SUCCEEDED;
        }

        //*************************************************************************
        //函数名称：GetOscilloscopeParameterUnitSet
        //函数功能：获取示波器单位集合
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan&Lilbert
        //更新时间：2020.06.15&2022.08.05
        //*************************************************************************
        public static void GetOscilloscopeParameterUnitSet()
        {
            SoftwareStateParameterSet.dicAcquisitionUint = new Dictionary<string, string>();

            SoftwareStateParameterSet.dicAcquisitionUint.Add("停用", "");
            SoftwareStateParameterSet.dicAcquisitionUint.Add("母线电压", "V");
            SoftwareStateParameterSet.dicAcquisitionUint.Add("母线电流", "mA");

            SoftwareStateParameterSet.dicAcquisitionUint.Add("位置指令", SelectUnit.Position);
            SoftwareStateParameterSet.dicAcquisitionUint.Add("位置反馈", SelectUnit.Position);
            SoftwareStateParameterSet.dicAcquisitionUint.Add("位置差值", SelectUnit.Position);
            SoftwareStateParameterSet.dicAcquisitionUint.Add("位置增量", SelectUnit.Position);           //由lilbert于2022年8月5日添加位置增量
            SoftwareStateParameterSet.dicAcquisitionUint.Add("控制器下发位置指令", SelectUnit.Position);

            SoftwareStateParameterSet.dicAcquisitionUint.Add("位置环输出速度指令", SelectUnit.Speed);
            SoftwareStateParameterSet.dicAcquisitionUint.Add("速度指令", SelectUnit.Speed);
            SoftwareStateParameterSet.dicAcquisitionUint.Add("速度反馈", SelectUnit.Speed);
            SoftwareStateParameterSet.dicAcquisitionUint.Add("速度差值", SelectUnit.Speed);

            SoftwareStateParameterSet.dicAcquisitionUint.Add("速度环输出转矩指令", "‰");
            SoftwareStateParameterSet.dicAcquisitionUint.Add("转矩指令", "‰");
            SoftwareStateParameterSet.dicAcquisitionUint.Add("转矩反馈", "‰");

            SoftwareStateParameterSet.dicAcquisitionUint.Add("U相电流", "mA");
            SoftwareStateParameterSet.dicAcquisitionUint.Add("V相电流", "mA");
            SoftwareStateParameterSet.dicAcquisitionUint.Add("W相电流", "mA");
            SoftwareStateParameterSet.dicAcquisitionUint.Add("D轴指令电流", "mA");
            SoftwareStateParameterSet.dicAcquisitionUint.Add("Q轴指令电流", "mA");
            SoftwareStateParameterSet.dicAcquisitionUint.Add("D轴反馈电流", "mA");
            SoftwareStateParameterSet.dicAcquisitionUint.Add("Q轴反馈电流", "mA");
            SoftwareStateParameterSet.dicAcquisitionUint.Add("机械角度", "Pulse");
            SoftwareStateParameterSet.dicAcquisitionUint.Add("电角度", "Pulse");

            SoftwareStateParameterSet.dicAcquisitionUint.Add("速度前馈", SelectUnit.Speed);
            SoftwareStateParameterSet.dicAcquisitionUint.Add("转矩前馈", "‰");

            SoftwareStateParameterSet.dicAcquisitionUint.Add("编码器输出速度", SelectUnit.Speed);
            SoftwareStateParameterSet.dicAcquisitionUint.Add("观测器输出速度", SelectUnit.Speed);
            SoftwareStateParameterSet.dicAcquisitionUint.Add("滤波后位置指令", SelectUnit.Position);
            SoftwareStateParameterSet.dicAcquisitionUint.Add("前馈后速度指令", SelectUnit.Speed);
            SoftwareStateParameterSet.dicAcquisitionUint.Add("陷波后Q轴电流指令", "mA");
            SoftwareStateParameterSet.dicAcquisitionUint.Add("单圈编码器值", "Pulse");
            SoftwareStateParameterSet.dicAcquisitionUint.Add("多圈编码器值", "Pulse");

            SoftwareStateParameterSet.dicAcquisitionUint.Add("故障标志位", "1");
            SoftwareStateParameterSet.dicAcquisitionUint.Add("警告标志位", "1");
            SoftwareStateParameterSet.dicAcquisitionUint.Add("抱闸控制位", "1");
            SoftwareStateParameterSet.dicAcquisitionUint.Add("控制字", "1");
            SoftwareStateParameterSet.dicAcquisitionUint.Add("状态字", "1");

            SoftwareStateParameterSet.dicAcquisitionUint.Add("Debug参数1", "1");
            SoftwareStateParameterSet.dicAcquisitionUint.Add("Debug参数2", "1");
            SoftwareStateParameterSet.dicAcquisitionUint.Add("Debug参数3", "1");
            SoftwareStateParameterSet.dicAcquisitionUint.Add("Debug参数4", "1");

        }

        //*************************************************************************
        //函数名称：GetExchangeValueByCurrentUnit
        //函数功能：获取单位转换值
        //
        //输入参数：bool bDirection      转换方向
        //                 string strUnit          当前单位
        //         
        //输出参数：None
        //        
        //编码作者：Ryan&Lilbert
        //更新时间：2020.06.15&2022.12.27
        //*************************************************************************
        public static double GetExchangeValueByCurrentUnit(bool bDirection, string strUnit)
        {
            string strDefaultUnit = null;
            List<UnitExchangedSet> lstUnitExchanged = new List<UnitExchangedSet>();
            List<string> lstPositionUnit = new List<string> { "cnt", "revs", "deg", "mm" }; //由Lilbert增加直线电机单位mm、mm/s、mm/s^2
            List<string> lstTorqueUnit = new List<string> { "0.1%额定扭矩", "Nm" };
            List<string> lstSpeedUnit = new List<string> { "cnt/s", "rpm", "rps", "deg/s", "mm/s" };
            List<string> lstAccelerationUnit = new List<string> { "cnt/s^2", "rpm/s", "rps/s", "deg/s^2", "mm/s^2" };
            List<List<string>> lstDefaultUnit = new List<List<string>>() { lstPositionUnit, lstTorqueUnit, lstSpeedUnit, lstAccelerationUnit };

            try
            {
                if (CurrentUnit.EncodeType == "Absolute")
                {
                    CurrentUnit.ListAbsolute.ForEach(item => lstUnitExchanged.Add(item));
                }
                else
                {
                    CurrentUnit.ListIncremental.ForEach(item => lstUnitExchanged.Add(item));
                }

                for (int i = 0; i < lstDefaultUnit.Count; i++)
                {
                    if (!string.IsNullOrEmpty(lstDefaultUnit[i].Where(o => o == strUnit).FirstOrDefault()))
                    {
                        switch (i)
                        {
                            case 0:
                                strDefaultUnit = DefaultUnit.PositionUnit;
                                break;
                            case 1:
                                strDefaultUnit = DefaultUnit.TorqueUnit;
                                break;
                            case 2:
                                strDefaultUnit = DefaultUnit.SpeedUnit;
                                break;
                            case 3:
                                strDefaultUnit = DefaultUnit.AccelerationUnit;
                                break;
                            default:
                                strDefaultUnit = null;
                                break;
                        }

                        break;
                    }
                }

                if (string.IsNullOrEmpty(strUnit) || string.IsNullOrEmpty(strDefaultUnit))
                {
                    return 1;
                }
                else
                {
                    if (bDirection)
                    {
                        strUnit = strDefaultUnit + "-" + strUnit;
                    }
                    else
                    {
                        strUnit = strUnit + "-" + strDefaultUnit;
                    }

                    UnitExchangedSet unitExchangedSet = lstUnitExchanged.Where(o => o.Content == strUnit).FirstOrDefault<UnitExchangedSet>();
                    if (unitExchangedSet != null)
                    {
                        return unitExchangedSet.Value;
                    }
                    else
                    {
                        return 1;
                    }
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OTHERSHELPER_GET_EXCHANGE_VALUE_BY_CURRENT_UNIT, "GetExchangeValueByCurrentUnit", ex);
                return 1;
            }
        }

        //*************************************************************************
        //函数名称：GetExchangeValueByCurrentUnit
        //函数功能：获取单位转换值
        //
        //输入参数：bool bDirection      转换方向
        //                 string strUnit          当前单位
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.04.26
        //*************************************************************************
        public static double GetExchangeValueByCurrentUnit_FaultAcquisition(bool bDirection, string strUnit)
        {
            string strDefaultUnit = null;
            List<UnitExchangedSet> lstUnitExchanged = new List<UnitExchangedSet>();
            List<string> lstPositionUnit = new List<string> { "cnt", "revs", "deg", "mm" }; //由Lilbert增加直线电机单位mm、mm/s、mm/s^2
            List<string> lstTorqueUnit = new List<string> { "0.1%额定扭矩", "Nm" };
            List<string> lstSpeedUnit = new List<string> { "cnt/s", "rpm", "rps", "deg/s", "mm/s" };
            List<string> lstAccelerationUnit = new List<string> { "cnt/s^2", "rpm/s", "rps/s", "deg/s^2", "mm/s^2" };
            List<List<string>> lstDefaultUnit = new List<List<string>>() { lstPositionUnit, lstTorqueUnit, lstSpeedUnit, lstAccelerationUnit };

            try
            {
                if (CurrentUnit.EncodeType == "Absolute")
                {
                    CurrentUnit.ListAbsolute.ForEach(item => lstUnitExchanged.Add(item));
                }
                else
                {
                    CurrentUnit.ListIncremental.ForEach(item => lstUnitExchanged.Add(item));
                }

                for (int i = 0; i < lstDefaultUnit.Count; i++)
                {
                    if (!string.IsNullOrEmpty(lstDefaultUnit[i].Where(o => o == strUnit).FirstOrDefault()))
                    {
                        switch (i)
                        {
                            case 0:
                                strDefaultUnit = DefaultUnit.PositionUnit;
                                break;
                            case 1:
                                strDefaultUnit = DefaultUnit.TorqueUnit;
                                break;
                            case 2:
                                strDefaultUnit = DefaultUnit.SpeedUnit;
                                break;
                            case 3:
                                strDefaultUnit = DefaultUnit.AccelerationUnit;
                                break;
                            default:
                                strDefaultUnit = null;
                                break;
                        }

                        break;
                    }
                }

                if (string.IsNullOrEmpty(strUnit) || string.IsNullOrEmpty(strDefaultUnit))
                {
                    return 1;
                }
                else
                {
                    if (bDirection)
                    {
                        strUnit = strDefaultUnit + "-" + strUnit;
                    }
                    else
                    {
                        strUnit = strUnit + "-" + strDefaultUnit;
                    }

                    UnitExchangedSet unitExchangedSet = lstUnitExchanged.Where(o => o.Content == strUnit).FirstOrDefault<UnitExchangedSet>();
                    if (unitExchangedSet != null)
                    {
                        return unitExchangedSet.Value;
                    }
                    else
                    {
                        return 1;
                    }
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OTHERSHELPER_GET_EXCHANGE_VALUE_BY_CURRENT_UNIT, "GetExchangeValueByCurrentUnit", ex);
                return 1;
            }
        }

        //*************************************************************************
        //函数名称： GetDefaultUnit
        //函数功能：获取当前默认参数的单位
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.06.18
        //*************************************************************************
        public static void GetDefaultUnit()
        {
            GetCurrentUnit(bDefault: true);

            CurrentUnit.ListAbsolute = new List<UnitExchangedSet>();
            CurrentUnit.ListIncremental = new List<UnitExchangedSet>();

            //默认单位
            CurrentUnit.ListIncremental.Add(new UnitExchangedSet() { Content = "cnt-cnt", Value = 1 });
            CurrentUnit.ListIncremental.Add(new UnitExchangedSet() { Content = "cnt/s-cnt/s", Value = 1 });
            CurrentUnit.ListIncremental.Add(new UnitExchangedSet() { Content = "cnt/s^2-cnt/s^2", Value = 1 });
            CurrentUnit.ListIncremental.Add(new UnitExchangedSet() { Content = "0.1%额定扭矩-0.1%额定扭矩", Value = 1 });

            CurrentUnit.ListAbsolute.Add(new UnitExchangedSet() { Content = "cnt-cnt", Value = 1 });
            CurrentUnit.ListAbsolute.Add(new UnitExchangedSet() { Content = "cnt/s-cnt/s", Value = 1 });
            CurrentUnit.ListAbsolute.Add(new UnitExchangedSet() { Content = "cnt/s^2-cnt/s^2", Value = 1 });
            CurrentUnit.ListAbsolute.Add(new UnitExchangedSet() { Content = "0.1%额定扭矩-0.1%额定扭矩", Value = 1 });
        }

        //*************************************************************************
        //函数名称：GetSelectUnit
        //函数功能：获取当前选中的单位
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.08.24
        //*************************************************************************
        public static void GetSelectDefaultUnit()
        {
            SelectUnit.Position = DefaultUnit.PositionUnit;
            SelectUnit.Speed = DefaultUnit.SpeedUnit;
            SelectUnit.Torque = DefaultUnit.TorqueUnit;
            SelectUnit.Acceleration = DefaultUnit.AccelerationUnit;
        }

        //*************************************************************************
        //函数名称：GetCurrentUnit
        //函数功能：获取当前参数的单位
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.08.24
        //*************************************************************************
        public static void GetCurrentUnit(bool bDefault)
        {
            if (bDefault)
            {
                CurrentUnit.Position = DefaultUnit.PositionUnit;
                CurrentUnit.Speed = DefaultUnit.SpeedUnit;
                CurrentUnit.Torque = DefaultUnit.TorqueUnit;
                CurrentUnit.Acceleration = DefaultUnit.AccelerationUnit;
            }
            else
            {
                CurrentUnit.Position = SelectUnit.Position;
                CurrentUnit.Speed = SelectUnit.Speed;
                CurrentUnit.Torque = SelectUnit.Torque;
                CurrentUnit.Acceleration = SelectUnit.Acceleration;
            }
        }

        //*************************************************************************
        //函数名称：GetSelectedUnitFromFile
        //函数功能：获取配置文件的单位
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.06.18
        //*************************************************************************
        public static void GetSelectedUnit(bool bInitialized)
        {
            if (bInitialized)
            {
                SelectUnit.Position = IniHelper.IniReadValue("ServoStudio", "PositionUnit", FilePath.Ini);
                SelectUnit.Torque = IniHelper.IniReadValue("ServoStudio", "TorqueUnit", FilePath.Ini);
                SelectUnit.Speed = IniHelper.IniReadValue("ServoStudio", "SpeedUnit", FilePath.Ini);
                SelectUnit.Acceleration = IniHelper.IniReadValue("ServoStudio", "AccelerationUnit", FilePath.Ini);
            }
            else
            {
                SelectUnit.Position = ViewModelSet.Unit?.SelectedPositionUnit;
                SelectUnit.Torque = ViewModelSet.Unit?.SelectedTorqueUnit;
                SelectUnit.Speed = ViewModelSet.Unit?.SelectedSpeedUnit;
                SelectUnit.Acceleration = ViewModelSet.Unit?.SelectedAccelerationUnit;
                WriteSelectedUnitIntoFile();
            }

            GetCurrentUnit(bDefault: false);
        }

        //*************************************************************************
        //函数名称：WriteSelectedUnitIntoFile
        //函数功能：把单位写入配置文件
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.06.29
        //*************************************************************************
        public static void WriteSelectedUnitIntoFile()
        {
            IniHelper.IniWriteValue("ServoStudio", "PositionUnit", SelectUnit.Position, FilePath.Ini);
            IniHelper.IniWriteValue("ServoStudio", "TorqueUnit", SelectUnit.Torque, FilePath.Ini);
            IniHelper.IniWriteValue("ServoStudio", "SpeedUnit", SelectUnit.Speed, FilePath.Ini);
            IniHelper.IniWriteValue("ServoStudio", "AccelerationUnit", SelectUnit.Acceleration, FilePath.Ini);
        }

        //*************************************************************************
        //函数名称：WriteSelectedOscilloscopePresetIntoFile
        //函数功能：把示波器预配置写入配置文件
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.11.10
        //*************************************************************************
        public static void WriteSelectedOscilloscopePresetIntoFile()
        {
            IniHelper.IniWriteValue("ServoStudio", "SelectedOscilloscopePreset", GlobalCurrentInput.SelectedOscilloscopePreset, FilePath.Ini);
        }

        //*************************************************************************
        //函数名称：WriteOscilloscopeWaveformPathIntoFile
        //函数功能：把示波器波形路径写入配置文件
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.11.10
        //*************************************************************************
        public static void WriteOscilloscopeWaveformPathIntoFile()
        {
            IniHelper.IniWriteValue("ServoStudio", "OscilloscopeWaveformPath", GlobalCurrentInput.SelectedOscilloscopePreset, FilePath.Ini);
        }

        //*************************************************************************
        //函数名称：ExportParameterInfo
        //函数功能：导出参数信息
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan&Lilbert
        //更新时间：2020.06.28&2022.01.11
        //*************************************************************************
        public static int ExportParameterInfo()
        {
            int iRet = -1;
            string strFilePath = null;

            //获取文件保存路径
            iRet = ExcelHelper.GetWritePath(ref strFilePath, ExcelType.EtherCAT);
            if (iRet == RET.ERROR)
            {
                return RET.ERROR;
            }
            else if (iRet == RET.NO_EFFECT)
            {
                return RET.NO_EFFECT;
            }

            //更新导出数据集
            iRet = OthersHelper.DataTableExportUpdate(ref GlobalParameterSet.dt_Export);
            if (iRet != RET.SUCCEEDED)
            {
                return RET.ERROR;
            }

            //更新Excel配置文件
            //iRet = ExcelHelper.WriteIntoExcel(strFilePath, GlobalParameterSet.dt_Export, ExcelType.EtherCAT);
            iRet = ExcelHelper.WriteIntoExcel_For_Software(strFilePath, GlobalParameterSet.dt_Export, ExcelType.EtherCAT);    //由Lilbert添加
            if (iRet == RET.SUCCEEDED)
            {
                return 2002;
            }
            else
            {
                return 2003;
            }
        }

        //*************************************************************************
        //函数名称：ExportParameterInfo
        //函数功能：导出参数信息
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.05.17
        //*************************************************************************
        public static int ExportParameterInfo_ForImportConfig()
        {
            int iRet = -1;
            string strFilePath = null;

            //获取文件保存路径
            strFilePath = FilePath.ParameterLibrary + "配置文件.xlsx";

            //更新导出数据集
            iRet = OthersHelper.DataTableExportUpdate(ref GlobalParameterSet.dt_Export_ForCompare);
            if (iRet != RET.SUCCEEDED)
            {
                return RET.ERROR;
            }

            //更新Excel配置文件
            iRet = ExcelHelper.WriteIntoExcel_ForImportConfig(strFilePath, GlobalParameterSet.dt_Export_ForCompare, ExcelType.EtherCAT);
            if (iRet == RET.SUCCEEDED)
            {
                return 2002;
            }
            else
            {
                return 2003;
            }
        }

        //*************************************************************************
        //函数名称：CheckIsInputHex
        //函数功能：判断是否输入16进制数据
        //
        //输入参数：string strValue  输入数据
        //         
        //输出参数：true：是16进制
        //                 false：不是16进制
        //        
        //编码作者：Ryan
        //更新时间：2020.08.24
        //*************************************************************************
        public static bool CheckIsInputHex(string strValue)
        {
            try
            {
                if (string.IsNullOrEmpty(strValue))
                {
                    return false;
                }

                if (strValue.Length <= 2)
                {
                    return false;
                }

                if (strValue.Substring(0, 2).ToUpper() == "0X")
                {
                    if (IsNumberAndWord(strValue.Remove(0, 2)))
                    {
                        return true;
                    }
                    else
                    {
                        return false;
                    }
                }
                else
                {
                    return false;
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OTHERSHELPER_CHECK_IS_INPUT_HEX, "CheckIsInputHex", ex);
                return false;
            }
        }

        //*************************************************************************
        //函数名称：TransferHexToDecimal
        //函数功能：16进制数据转换10进制
        //
        //输入参数：string strValue  16进制数据
        //         
        //输出参数：string strValue 10进制数据
        //        
        //编码作者：Ryan
        //更新时间：2020.08.24
        //*************************************************************************
        public static string TransferHexToDecimal(string strValue, string strDataType)
        {

            if (string.IsNullOrEmpty(strValue) || string.IsNullOrEmpty(strDataType))
            {
                return null;
            }

            switch (strDataType.ToUpper())
            {
                case "INT32":
                    return Convert.ToString(Convert.ToInt32(strValue.Replace("0x", ""), 16));
                case "UINT32":
                    return Convert.ToString(Convert.ToUInt32(strValue.Replace("0x", ""), 16));
                case "INT16":
                    return Convert.ToString(Convert.ToInt16(strValue.Replace("0x", ""), 16));
                case "UINT16":
                    return Convert.ToString(Convert.ToUInt16(strValue.Replace("0x", ""), 16));
                case "INT8":
                    return Convert.ToString(Convert.ToSByte(strValue.Replace("0x", ""), 16));
                case "UINT8":
                    return Convert.ToString(Convert.ToByte(strValue.Replace("0x", ""), 16));
                default:
                    return null;
            }
        }

        //*************************************************************************
        //函数名称：SystemParameterInitialize
        //函数功能：系统参数初始化
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.09.07
        //*************************************************************************
        public static void SystemParameterInitialize()
        {
            Dictionary<string, string> dicParameterInfo = new Dictionary<string, string>();
            List<ParameterReadWriteSet> lstParameterInfo = new List<ParameterReadWriteSet>();
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            //获取参数详细信息          
            dicParameterInfo.Add("System Prm Init", "2");
            OthersHelper.GetParameterForWrite(dicParameterInfo, ref lstParameterInfo);

            //获取发送任务信息
            ParameterReadWriteModel.GetIndexAndDataType(lstParameterInfo, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true, IsEEPROM: false);

            //下达任务
            ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterWrite(PageName.MOTORFEEDBACK, TaskName.SystemParameterInitialize, lstTransmittingDataInfo);
        }

        //*************************************************************************
        //函数名称：OperatingControl
        //函数功能：运行控制
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2022.11.21
        //*************************************************************************
        public static void OperatingControl(string strOperation, string strValue, string strTaskName, string strPageName, bool bEEPROM)
        {
            int iRet = -1;
            Dictionary<string, string> dicParameterInfo = new Dictionary<string, string>();
            List<ParameterReadWriteSet> lstParameterInfo = new List<ParameterReadWriteSet>();
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            //判断串口状态
            iRet = HexHelper.CheckSerialPortStatus();
            if (iRet != RET.SUCCEEDED)
            {
                return;
            }

            //字段Key-Value字段不允许为空
            if (string.IsNullOrEmpty(strOperation) || string.IsNullOrEmpty(strValue))
            {
                return;
            }

            //获取参数详细信息  
            dicParameterInfo.Add(strOperation, strValue);
            iRet = OthersHelper.GetParameterForWrite(dicParameterInfo, ref lstParameterInfo);
            if (iRet != RET.SUCCEEDED)
            {
                return;
            }

            //获取发送任务信息
            ParameterReadWriteModel.GetIndexAndDataType(lstParameterInfo, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true, IsEEPROM: bEEPROM);

            //下达任务
            ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterWrite(strPageName, strTaskName, lstTransmittingDataInfo);
        }

        //*************************************************************************
        //函数名称：OperatingControl
        //函数功能：运行控制
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.09.09
        //*************************************************************************
        public static void OperatingControl(string strOperation, string strValue, string strTaskName, string strPageName)
        {
            int iRet = -1;
            Dictionary<string, string> dicParameterInfo = new Dictionary<string, string>();
            List<ParameterReadWriteSet> lstParameterInfo = new List<ParameterReadWriteSet>();
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            //判断串口状态
            iRet = HexHelper.CheckSerialPortStatus();
            if (iRet != RET.SUCCEEDED)
            {
                return;
            }

            //字段Key-Value字段不允许为空
            if (string.IsNullOrEmpty(strOperation) || string.IsNullOrEmpty(strValue))
            {
                return;
            }

            //获取参数详细信息  
            dicParameterInfo.Add(strOperation, strValue);
            iRet = OthersHelper.GetParameterForWrite(dicParameterInfo, ref lstParameterInfo);
            if (iRet != RET.SUCCEEDED)
            {
                return;
            }

            //获取发送任务信息
            ParameterReadWriteModel.GetIndexAndDataType(lstParameterInfo, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true, IsEEPROM: false);

            //下达任务
            ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterWrite(strPageName, strTaskName, lstTransmittingDataInfo);
        }

        //*************************************************************************
        //函数名称：GetMotEstStateStatus
        //函数功能：获取内部自学习状态
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2022.11.16
        //*************************************************************************
        public static void GetMotEstStateStatus(string TaskName)
        {
            Dictionary<string, string> dicParameterInfo = new Dictionary<string, string>();
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();
            ObservableCollection<ParameterReadWriteSet> obsParameterInfo_ForRead = new ObservableCollection<ParameterReadWriteSet>();

            //获取要读取的数据字典
            dicParameterInfo.Add("MotEstState", null);

            //获取参数详细信息
            OthersHelper.GetParameterForRead(dicParameterInfo, ref obsParameterInfo_ForRead);

            //获取发送任务信息
            ParameterReadWriteModel.GetIndexAndDataType(obsParameterInfo_ForRead, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true);

            //下达任务
            ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterRead(SoftwareStateParameterSet.CurrentPageName, TaskName, lstTransmittingDataInfo);
        }

        //*************************************************************************
        //函数名称：GetActualEnableStatus
        //函数功能：获取内部使能状态
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.02
        //*************************************************************************
        public static void GetActualEnableStatus(string TaskName)
        {
            Dictionary<string, string> dicParameterInfo = new Dictionary<string, string>();
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();
            ObservableCollection<ParameterReadWriteSet> obsParameterInfo_ForRead = new ObservableCollection<ParameterReadWriteSet>();

            //获取要读取的数据字典
            dicParameterInfo.Add("Actual Enable", null);

            //获取参数详细信息
            OthersHelper.GetParameterForRead(dicParameterInfo, ref obsParameterInfo_ForRead);

            //获取发送任务信息
            ParameterReadWriteModel.GetIndexAndDataType(obsParameterInfo_ForRead, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true);

            //下达任务
            ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterRead(SoftwareStateParameterSet.CurrentPageName, TaskName, lstTransmittingDataInfo);
        }

        //*************************************************************************
        //函数名称：CheckIsMonitoredStatus
        //函数功能：参数被监控状态
        //
        //输入参数：string In_strName     参数名称
        //       
        //输出参数：bool bStatus          是否被监控
        //        
        //编码作者：Ryan
        //更新时间：2019.11.14
        //*************************************************************************
        public static bool CheckIsMonitoredStatus(string In_strName)
        {
            string strStatus = null;

            strStatus = GetCellValueFromDataTable(GlobalParameterSet.dt_Monitor, "Name", In_strName, "IsMonitored");
            if (string.IsNullOrEmpty(strStatus))
            {
                return false;
            }
            else
            {
                if (strStatus.ToUpper() == "TRUE")
                {
                    return true;
                }
                else
                {
                    return false;
                }
            }
        }

        //*************************************************************************
        //函数名称：GetSelectedSlaveAxisID
        //函数功能：获取选中的轴地址信息
        //
        //输入参数：int selectedConfigSlaveID     选中的配置从站信息
        //          int selectedConfigSlaveID     选中的配置轴信息
        //          string selectedSlaveID        选中的从站地址
        //          string selectedAxisID         选中的轴地址
        //输出参数：void
        //        
        //编码作者：Lilbert
        //更新时间：2023.10.26
        //*************************************************************************
        public static void GetSelectedSlaveAxisID(int selectedConfigSlaveID, int selectedConfigAxisID, string selectedAxisID)
        {
            #region 2合一伺服
            if (selectedConfigSlaveID == 2 && selectedConfigAxisID == 2)
            {
                if (selectedAxisID == "AXIS-1")
                {
                    SoftwareStateParameterSet.SlaveID = "00";
                    SoftwareStateParameterSet.AxisID = "00";
                }
                else if (selectedAxisID == "AXIS-2")
                {
                    SoftwareStateParameterSet.SlaveID = "00";
                    SoftwareStateParameterSet.AxisID = "01";
                }
            }
            #endregion
            #region 4合一伺服
            if (selectedConfigSlaveID == 2 && selectedConfigAxisID == 4)
            {
                if (selectedAxisID == "AXIS-1")
                {
                    SoftwareStateParameterSet.SlaveID = "00";
                    SoftwareStateParameterSet.AxisID = "00";
                }
                else if (selectedAxisID == "AXIS-2")
                {
                    SoftwareStateParameterSet.SlaveID = "00";
                    SoftwareStateParameterSet.AxisID = "01";
                }
                else if (selectedAxisID == "AXIS-3")
                {
                    SoftwareStateParameterSet.SlaveID = "01";
                    SoftwareStateParameterSet.AxisID = "00";
                }
                else if (selectedAxisID == "AXIS-4")
                {
                    SoftwareStateParameterSet.SlaveID = "01";
                    SoftwareStateParameterSet.AxisID = "01";
                }
            }
            #endregion
            #region 6合一伺服
            if (selectedConfigSlaveID == 3 && selectedConfigAxisID == 6)
            {
                if (selectedAxisID == "AXIS-1")
                {
                    SoftwareStateParameterSet.SlaveID = "00";
                    SoftwareStateParameterSet.AxisID = "00";
                }
                else if (selectedAxisID == "AXIS-2")
                {
                    SoftwareStateParameterSet.SlaveID = "00";
                    SoftwareStateParameterSet.AxisID = "01";
                }
                else if (selectedAxisID == "AXIS-3")
                {
                    SoftwareStateParameterSet.SlaveID = "01";
                    SoftwareStateParameterSet.AxisID = "00";
                }
                else if (selectedAxisID == "AXIS-4")
                {
                    SoftwareStateParameterSet.SlaveID = "01";
                    SoftwareStateParameterSet.AxisID = "01";
                }
                else if (selectedAxisID == "AXIS-5")
                {
                    SoftwareStateParameterSet.SlaveID = "10";
                    SoftwareStateParameterSet.AxisID = "00";
                }
                else if (selectedAxisID == "AXIS-6")
                {
                    SoftwareStateParameterSet.SlaveID = "10";
                    SoftwareStateParameterSet.AxisID = "01";
                }
            }
            #endregion

            #region 单轴伺服
            if ((selectedConfigSlaveID == 1 && selectedConfigAxisID == 1) || (selectedConfigSlaveID == 1 && selectedConfigAxisID == 2) || (selectedConfigSlaveID == 1 && selectedConfigAxisID == 3) ||
                (selectedConfigSlaveID == 1 && selectedConfigAxisID == 4) || (selectedConfigSlaveID == 1 && selectedConfigAxisID == 5) || (selectedConfigSlaveID == 1 && selectedConfigAxisID == 6) ||
                (selectedConfigSlaveID == 1 && selectedConfigAxisID == 7) || (selectedConfigSlaveID == 1 && selectedConfigAxisID == 8) || (selectedConfigSlaveID == 1 && selectedConfigAxisID == 9) ||
                (selectedConfigSlaveID == 1 && selectedConfigAxisID == 10) || (selectedConfigSlaveID == 1 && selectedConfigAxisID == 11) || (selectedConfigSlaveID == 1 && selectedConfigAxisID == 12) ||
                (selectedConfigSlaveID == 1 && selectedConfigAxisID == 13) || (selectedConfigSlaveID == 1 && selectedConfigAxisID == 14) || (selectedConfigSlaveID == 1 && selectedConfigAxisID == 15) ||
                (selectedConfigSlaveID == 1 && selectedConfigAxisID == 16) || (selectedConfigSlaveID == 1 && selectedConfigAxisID == 17) || (selectedConfigSlaveID == 1 && selectedConfigAxisID == 18) ||
                (selectedConfigSlaveID == 1 && selectedConfigAxisID == 19) || (selectedConfigSlaveID == 1 && selectedConfigAxisID == 20) || (selectedConfigSlaveID == 1 && selectedConfigAxisID == 21) ||
                (selectedConfigSlaveID == 1 && selectedConfigAxisID == 22) || (selectedConfigSlaveID == 1 && selectedConfigAxisID == 23) || (selectedConfigSlaveID == 1 && selectedConfigAxisID == 24) ||
                (selectedConfigSlaveID == 1 && selectedConfigAxisID == 25) || (selectedConfigSlaveID == 1 && selectedConfigAxisID == 26) || (selectedConfigSlaveID == 1 && selectedConfigAxisID == 27) ||
                (selectedConfigSlaveID == 1 && selectedConfigAxisID == 28) || (selectedConfigSlaveID == 1 && selectedConfigAxisID == 29) || (selectedConfigSlaveID == 1 && selectedConfigAxisID == 30) ||
                (selectedConfigSlaveID == 1 && selectedConfigAxisID == 31) || (selectedConfigSlaveID == 1 && selectedConfigAxisID == 32) )

            {
                if (selectedAxisID == "AXIS-1")
                {
                    SoftwareStateParameterSet.SlaveID = "00";
                    SoftwareStateParameterSet.AxisID = "00";
                }
                else if (selectedAxisID == "AXIS-2")
                {
                    SoftwareStateParameterSet.SlaveID = "01";
                    SoftwareStateParameterSet.AxisID = "00";
                }
                else if (selectedAxisID == "AXIS-3")
                {
                    SoftwareStateParameterSet.SlaveID = "02";
                    SoftwareStateParameterSet.AxisID = "00";
                }
                else if (selectedAxisID == "AXIS-4")
                {
                    SoftwareStateParameterSet.SlaveID = "03";
                    SoftwareStateParameterSet.AxisID = "00";
                }
                else if (selectedAxisID == "AXIS-5")
                {
                    SoftwareStateParameterSet.SlaveID = "04";
                    SoftwareStateParameterSet.AxisID = "00";
                }
                else if (selectedAxisID == "AXIS-6")
                {
                    SoftwareStateParameterSet.SlaveID = "05";
                    SoftwareStateParameterSet.AxisID = "00";
                }
                else if (selectedAxisID == "AXIS-7")
                {
                    SoftwareStateParameterSet.SlaveID = "06";
                    SoftwareStateParameterSet.AxisID = "00";
                }
                else if (selectedAxisID == "AXIS-8")
                {
                    SoftwareStateParameterSet.SlaveID = "07";
                    SoftwareStateParameterSet.AxisID = "00";
                }
                else if (selectedAxisID == "AXIS-9")
                {
                    SoftwareStateParameterSet.SlaveID = "08";
                    SoftwareStateParameterSet.AxisID = "00";
                }
                else if (selectedAxisID == "AXIS-10")
                {
                    SoftwareStateParameterSet.SlaveID = "09";
                    SoftwareStateParameterSet.AxisID = "00";
                }
                else if (selectedAxisID == "AXIS-11")
                {
                    SoftwareStateParameterSet.SlaveID = "0A";
                    SoftwareStateParameterSet.AxisID = "00";
                }
                else if (selectedAxisID == "AXIS-12")
                {
                    SoftwareStateParameterSet.SlaveID = "0B";
                    SoftwareStateParameterSet.AxisID = "00";
                }
                else if (selectedAxisID == "AXIS-13")
                {
                    SoftwareStateParameterSet.SlaveID = "0C";
                    SoftwareStateParameterSet.AxisID = "00";
                }
                else if (selectedAxisID == "AXIS-14")
                {
                    SoftwareStateParameterSet.SlaveID = "0D";
                    SoftwareStateParameterSet.AxisID = "00";
                }
                else if (selectedAxisID == "AXIS-15")
                {
                    SoftwareStateParameterSet.SlaveID = "0E";
                    SoftwareStateParameterSet.AxisID = "00";
                }
                else if (selectedAxisID == "AXIS-16")
                {
                    SoftwareStateParameterSet.SlaveID = "0F";
                    SoftwareStateParameterSet.AxisID = "00";
                }
                else if (selectedAxisID == "AXIS-17")
                {
                    SoftwareStateParameterSet.SlaveID = "10";
                    SoftwareStateParameterSet.AxisID = "00";
                }
                else if (selectedAxisID == "AXIS-18")
                {
                    SoftwareStateParameterSet.SlaveID = "11";
                    SoftwareStateParameterSet.AxisID = "00";
                }
                else if (selectedAxisID == "AXIS-19")
                {
                    SoftwareStateParameterSet.SlaveID = "12";
                    SoftwareStateParameterSet.AxisID = "00";
                }
                else if (selectedAxisID == "AXIS-20")
                {
                    SoftwareStateParameterSet.SlaveID = "13";
                    SoftwareStateParameterSet.AxisID = "00";
                }
                else if (selectedAxisID == "AXIS-21")
                {
                    SoftwareStateParameterSet.SlaveID = "14";
                    SoftwareStateParameterSet.AxisID = "00";
                }
                else if (selectedAxisID == "AXIS-22")
                {
                    SoftwareStateParameterSet.SlaveID = "15";
                    SoftwareStateParameterSet.AxisID = "00";
                }
                else if (selectedAxisID == "AXIS-23")
                {
                    SoftwareStateParameterSet.SlaveID = "16";
                    SoftwareStateParameterSet.AxisID = "00";
                }
                else if (selectedAxisID == "AXIS-24")
                {
                    SoftwareStateParameterSet.SlaveID = "17";
                    SoftwareStateParameterSet.AxisID = "00";
                }
                else if (selectedAxisID == "AXIS-25")
                {
                    SoftwareStateParameterSet.SlaveID = "18";
                    SoftwareStateParameterSet.AxisID = "00";
                }
                else if (selectedAxisID == "AXIS-26")
                {
                    SoftwareStateParameterSet.SlaveID = "19";
                    SoftwareStateParameterSet.AxisID = "00";
                }
                else if (selectedAxisID == "AXIS-27")
                {
                    SoftwareStateParameterSet.SlaveID = "1A";
                    SoftwareStateParameterSet.AxisID = "00";
                }
                else if (selectedAxisID == "AXIS-28")
                {
                    SoftwareStateParameterSet.SlaveID = "1B";
                    SoftwareStateParameterSet.AxisID = "00";
                }
                else if (selectedAxisID == "AXIS-29")
                {
                    SoftwareStateParameterSet.SlaveID = "1C";
                    SoftwareStateParameterSet.AxisID = "00";
                }
                else if (selectedAxisID == "AXIS-30")
                {
                    SoftwareStateParameterSet.SlaveID = "1D";
                    SoftwareStateParameterSet.AxisID = "00";
                }
                else if (selectedAxisID == "AXIS-31")
                {
                    SoftwareStateParameterSet.SlaveID = "1E";
                    SoftwareStateParameterSet.AxisID = "00";
                }
                else if (selectedAxisID == "AXIS-32")
                {
                    SoftwareStateParameterSet.SlaveID = "1F";
                    SoftwareStateParameterSet.AxisID = "00";
                }
            }           
            #endregion
        }

        //*************************************************************************
        //函数名称：GetSelectedAxisID
        //函数功能：获取选中的轴地址信息
        //
        //输入参数：string selectedAxisID     选中的轴地址
        //       
        //输出参数：void
        //        
        //编码作者：Ryan
        //更新时间：2020.03.08
        //*************************************************************************
        public static void GetSelectedAxisID(string selectedAxisID)
        {
            switch (selectedAxisID)
            {
                case "AXIS-1":
                    //SoftwareStateParameterSet.StationID = "00";
                    SoftwareStateParameterSet.SlaveID = "00";
                    SoftwareStateParameterSet.AxisID = "00";
                    break;
                case "AXIS-2":
                    //SoftwareStateParameterSet.StationID = "00";
                    SoftwareStateParameterSet.SlaveID = "00";
                    SoftwareStateParameterSet.AxisID = "01";
                    break;
                case "AXIS-3":
                    //SoftwareStateParameterSet.StationID = "01";
                    SoftwareStateParameterSet.SlaveID = "01";
                    SoftwareStateParameterSet.AxisID = "00";
                    break;
                case "AXIS-4":
                    //SoftwareStateParameterSet.StationID = "01";
                    SoftwareStateParameterSet.SlaveID = "01";
                    SoftwareStateParameterSet.AxisID = "01";
                    break;
                case "AXIS-5":
                    //SoftwareStateParameterSet.StationID = "10";
                    SoftwareStateParameterSet.SlaveID = "10";
                    SoftwareStateParameterSet.AxisID = "00";
                    break;
                case "AXIS-6":
                    //SoftwareStateParameterSet.StationID = "10";
                    SoftwareStateParameterSet.SlaveID = "10";
                    SoftwareStateParameterSet.AxisID = "01";
                    break;
                default:
                    break;
            }
        }

        //*************************************************************************
        //函数名称：GetSelectedStationAxisID
        //函数功能：获取选中的从站地址和轴地址信息
        //
        //输入参数：int selectedConfigSlaveID      选中的配置从站信息
        //          int selectedConfigAxisID       选中的配置轴信息
        //          string slaveID                 选中的从站地址
        //          string axisID                  选中的轴地址
        //       
        //输出参数：void
        //        
        //编码作者：Lilbert
        //更新时间：2023.10.27
        //*************************************************************************
        public static string GetSelectedStationAxisID(int selectedConfigSlaveID, int selectedConfigAxisID, string slaveID, string axisID)
        {
            #region 2合一伺服
            if (selectedConfigSlaveID == 2 && selectedConfigAxisID == 2)
            {
                if (slaveID == "00" && axisID == "00")
                {
                    return "AXIS-1";
                }
                else if (slaveID == "00" && axisID == "01")
                {
                    return "AXIS-2";
                }
                else
                {
                    return "AXIS-1";
                }
            }
            #endregion
            #region 4合一伺服
            else if (selectedConfigSlaveID == 2 && selectedConfigAxisID == 4)
            {
                if (slaveID == "00" && axisID == "00")
                {
                    return "AXIS-1";
                }
                else if (slaveID == "00" && axisID == "01")
                {
                    return "AXIS-2";
                }
                else if (slaveID == "01" && axisID == "00")
                {
                    return "AXIS-3";
                }
                else if (slaveID == "01" && axisID == "01")
                {
                    return "AXIS-4";
                }
                else
                {
                    return "AXIS-1";
                }
            }
            #endregion
            #region 6合一伺服
            else if (selectedConfigSlaveID == 3 && selectedConfigAxisID == 6)
            {
                if (slaveID == "00" && axisID == "00")
                {
                    return "AXIS-1";
                }
                else if (slaveID == "00" && axisID == "01")
                {
                    return "AXIS-2";
                }
                else if (slaveID == "01" && axisID == "00")
                {
                    return "AXIS-3";
                }
                else if (slaveID == "01" && axisID == "01")
                {
                    return "AXIS-4";
                }
                else if (slaveID == "10" && axisID == "00")
                {
                    return "AXIS-5";
                }
                else if (slaveID == "10" && axisID == "01")
                {
                    return "AXIS-6";
                }
                else
                {
                    return "AXIS-1";
                }
            }
            #endregion

            #region 单轴伺服
            else if ((selectedConfigSlaveID == 1 && selectedConfigAxisID == 1) || (selectedConfigSlaveID == 1 && selectedConfigAxisID == 2) || (selectedConfigSlaveID == 1 && selectedConfigAxisID == 3) ||
                (selectedConfigSlaveID == 1 && selectedConfigAxisID == 4) || (selectedConfigSlaveID == 1 && selectedConfigAxisID == 5) || (selectedConfigSlaveID == 1 && selectedConfigAxisID == 6) ||
                (selectedConfigSlaveID == 1 && selectedConfigAxisID == 7) || (selectedConfigSlaveID == 1 && selectedConfigAxisID == 8) || (selectedConfigSlaveID == 1 && selectedConfigAxisID == 9) ||
                (selectedConfigSlaveID == 1 && selectedConfigAxisID == 10) || (selectedConfigSlaveID == 1 && selectedConfigAxisID == 11) || (selectedConfigSlaveID == 1 && selectedConfigAxisID == 12) ||
                (selectedConfigSlaveID == 1 && selectedConfigAxisID == 13) || (selectedConfigSlaveID == 1 && selectedConfigAxisID == 14) || (selectedConfigSlaveID == 1 && selectedConfigAxisID == 15) ||
                (selectedConfigSlaveID == 1 && selectedConfigAxisID == 16) || (selectedConfigSlaveID == 1 && selectedConfigAxisID == 17) || (selectedConfigSlaveID == 1 && selectedConfigAxisID == 18) ||
                (selectedConfigSlaveID == 1 && selectedConfigAxisID == 19) || (selectedConfigSlaveID == 1 && selectedConfigAxisID == 20) || (selectedConfigSlaveID == 1 && selectedConfigAxisID == 21) ||
                (selectedConfigSlaveID == 1 && selectedConfigAxisID == 22) || (selectedConfigSlaveID == 1 && selectedConfigAxisID == 23) || (selectedConfigSlaveID == 1 && selectedConfigAxisID == 24) ||
                (selectedConfigSlaveID == 1 && selectedConfigAxisID == 25) || (selectedConfigSlaveID == 1 && selectedConfigAxisID == 26) || (selectedConfigSlaveID == 1 && selectedConfigAxisID == 27) ||
                (selectedConfigSlaveID == 1 && selectedConfigAxisID == 28) || (selectedConfigSlaveID == 1 && selectedConfigAxisID == 29) || (selectedConfigSlaveID == 1 && selectedConfigAxisID == 30) ||
                (selectedConfigSlaveID == 1 && selectedConfigAxisID == 31) || (selectedConfigSlaveID == 1 && selectedConfigAxisID == 32) )
            {
                if (slaveID == "00" && axisID == "00")
                {
                    return "AXIS-1";
                }
                else if (slaveID == "01" && axisID == "00")
                {
                    return "AXIS-2";
                }
                else if (slaveID == "02" && axisID == "00")
                {
                    return "AXIS-3";
                }
                else if (slaveID == "03" && axisID == "00")
                {
                    return "AXIS-4";
                }
                else if (slaveID == "04" && axisID == "00")
                {
                    return "AXIS-5";
                }
                else if (slaveID == "05" && axisID == "00")
                {
                    return "AXIS-6";
                }
                else if (slaveID == "06" && axisID == "00")
                {
                    return "AXIS-7";
                }
                else if (slaveID == "07" && axisID == "00")
                {
                    return "AXIS-8";
                }
                else if (slaveID == "08" && axisID == "00")
                {
                    return "AXIS-9";
                }
                else if (slaveID == "09" && axisID == "00")
                {
                    return "AXIS-10";
                }
                else if (slaveID == "0A" && axisID == "00")
                {
                    return "AXIS-11";
                }
                else if (slaveID == "0B" && axisID == "00")
                {
                    return "AXIS-12";
                }
                else if (slaveID == "0C" && axisID == "00")
                {
                    return "AXIS-13";
                }
                else if (slaveID == "0D" && axisID == "00")
                {
                    return "AXIS-14";
                }
                else if (slaveID == "0E" && axisID == "00")
                {
                    return "AXIS-15";
                }
                else if (slaveID == "0F" && axisID == "00")
                {
                    return "AXIS-16";
                }

                else if (slaveID == "10" && axisID == "00")
                {
                    return "AXIS-17";
                }
                else if (slaveID == "11" && axisID == "00")
                {
                    return "AXIS-18";
                }
                else if (slaveID == "12" && axisID == "00")
                {
                    return "AXIS-19";
                }
                else if (slaveID == "13" && axisID == "00")
                {
                    return "AXIS-20";
                }
                else if (slaveID == "14" && axisID == "00")
                {
                    return "AXIS-21";
                }
                else if (slaveID == "15" && axisID == "00")
                {
                    return "AXIS-22";
                }
                else if (slaveID == "16" && axisID == "00")
                {
                    return "AXIS-23";
                }
                else if (slaveID == "17" && axisID == "00")
                {
                    return "AXIS-24";
                }
                else if (slaveID == "18" && axisID == "00")
                {
                    return "AXIS-25";
                }
                else if (slaveID == "19" && axisID == "00")
                {
                    return "AXIS-26";
                }
                else if (slaveID == "1A" && axisID == "00")
                {
                    return "AXIS-27";
                }
                else if (slaveID == "1B" && axisID == "00")
                {
                    return "AXIS-28";
                }
                else if (slaveID == "1C" && axisID == "00")
                {
                    return "AXIS-29";
                }
                else if (slaveID == "1D" && axisID == "00")
                {
                    return "AXIS-30";
                }
                else if (slaveID == "1E" && axisID == "00")
                {
                    return "AXIS-31";
                }
                else if (slaveID == "1F" && axisID == "00")
                {
                    return "AXIS-32";
                }
                else
                {
                    return "AXIS-1";
                }
            }           
            #endregion
            #region 6合一伺服
            else
            {
                if (slaveID == "00" && axisID == "00")
                {
                    return "AXIS-1";
                }
                else if (slaveID == "00" && axisID == "01")
                {
                    return "AXIS-2";
                }
                else if (slaveID == "01" && axisID == "00")
                {
                    return "AXIS-3";
                }
                else if (slaveID == "01" && axisID == "01")
                {
                    return "AXIS-4";
                }
                else if (slaveID == "10" && axisID == "00")
                {
                    return "AXIS-5";
                }
                else if (slaveID == "10" && axisID == "01")
                {
                    return "AXIS-6";
                }
                else
                {
                    return "AXIS-1";
                }
            }
            #endregion
        }

        public static string GetSelectedAxisID(string stationID, string axisID)
        {
            if (stationID == "00" && axisID == "00")
            {
                return "AXIS-1";
            }
            else if (stationID == "00" && axisID == "01")
            {
                return "AXIS-2";
            }
            else if (stationID == "01" && axisID == "00")
            {
                return "AXIS-3";
            }
            else if (stationID == "01" && axisID == "01")
            {
                return "AXIS-4";
            }
            else if (stationID == "10" && axisID == "00")
            {
                return "AXIS-5";
            }
            else if (stationID == "10" && axisID == "01")
            {
                return "AXIS-6";
            }
            else
            {
                return "AXIS-1";
            }
        }
    }
}
